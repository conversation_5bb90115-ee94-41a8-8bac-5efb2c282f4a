{"name": "nurse-training-records", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.4.1", "cra-template": "1.2.0", "firebase": "^11.1.0", "firebase-admin": "^13.0.2", "firebase-functions": "^6.2.0", "formik": "^2.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-scripts": "^5.0.1", "webpack": "^5.97.1", "webpack-cli": "^5.1.4", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}