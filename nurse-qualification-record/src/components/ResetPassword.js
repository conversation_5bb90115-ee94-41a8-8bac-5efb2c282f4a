import React, { useState } from "react";
import { auth } from "../services/firebase";
import { updatePassword, reauthenticateWithCredential, EmailAuthProvider } from "firebase/auth";

function ResetPassword() {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [passwordChanged, setPasswordChanged] = useState(false);

  // Password visibility states
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const validatePasswords = () => {
    if (!currentPassword) {
      setError("Please enter your current password.");
      return false;
    }
    if (!newPassword) {
      setError("Please enter a new password.");
      return false;
    }
    if (newPassword.length < 6) {
      setError("New password must be at least 6 characters long.");
      return false;
    }
    if (newPassword !== confirmPassword) {
      setError("New passwords do not match.");
      return false;
    }
    if (currentPassword === newPassword) {
      setError("New password must be different from current password.");
      return false;
    }
    return true;
  };

  const handleChangePassword = async (e) => {
    e.preventDefault();
    setError("");
    setMessage("");
    setLoading(true);

    if (!validatePasswords()) {
      setLoading(false);
      return;
    }

    try {
      const user = auth.currentUser;
      if (!user) {
        setError("No user is currently logged in.");
        setLoading(false);
        return;
      }

      // Re-authenticate user with current password
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);

      // Update password
      await updatePassword(user, newPassword);

      setPasswordChanged(true);
      setMessage("Password changed successfully!");
      setError("");

      // Clear form
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");

    } catch (error) {
      console.error("Error changing password:", error);

      // Handle specific Firebase errors
      switch (error.code) {
        case "auth/wrong-password":
          setError("Current password is incorrect.");
          break;
        case "auth/weak-password":
          setError("New password is too weak. Please choose a stronger password.");
          break;
        case "auth/requires-recent-login":
          setError("Please log out and log back in before changing your password.");
          break;
        case "auth/too-many-requests":
          setError("Too many attempts. Please try again later.");
          break;
        default:
          setError("Failed to change password. Please try again.");
      }
      setMessage("");
    }
    setLoading(false);
  };

  const handleChangeAnother = () => {
    setPasswordChanged(false);
    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
    setMessage("");
    setError("");
  };

  return (
    <div style={containerStyle}>
      <h2 style={headerStyle}>Change Password</h2>

      {!passwordChanged ? (
        <div style={formContainerStyle}>
          <p style={instructionStyle}>
            Enter your current password and choose a new password to update your account.
          </p>

          <form onSubmit={handleChangePassword}>
            <div style={inputGroupStyle}>
              <label style={labelStyle}>Current Password:</label>
              <div style={passwordInputContainerStyle}>
                <input
                  type={showCurrentPassword ? "text" : "password"}
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  placeholder="Enter your current password"
                  required
                  style={passwordInputStyle}
                  disabled={loading}
                />
                <button
                  type="button"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  style={eyeButtonStyle}
                  disabled={loading}
                >
                  {showCurrentPassword ? "👁️" : "🙈"}
                </button>
              </div>
            </div>

            <div style={inputGroupStyle}>
              <label style={labelStyle}>New Password:</label>
              <div style={passwordInputContainerStyle}>
                <input
                  type={showNewPassword ? "text" : "password"}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="Enter your new password (min 6 characters)"
                  required
                  style={passwordInputStyle}
                  disabled={loading}
                  minLength="6"
                />
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  style={eyeButtonStyle}
                  disabled={loading}
                >
                  {showNewPassword ? "👁️" : "🙈"}
                </button>
              </div>
            </div>

            <div style={inputGroupStyle}>
              <label style={labelStyle}>Confirm New Password:</label>
              <div style={passwordInputContainerStyle}>
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm your new password"
                  required
                  style={passwordInputStyle}
                  disabled={loading}
                  minLength="6"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  style={eyeButtonStyle}
                  disabled={loading}
                >
                  {showConfirmPassword ? "👁️" : "🙈"}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              style={loading ? { ...buttonStyle, ...disabledButtonStyle } : buttonStyle}
            >
              {loading ? "Changing Password..." : "Change Password"}
            </button>
          </form>

          {error && <p style={errorStyle}>{error}</p>}
          {message && <p style={successStyle}>{message}</p>}
        </div>
      ) : (
        <div style={successContainerStyle}>
          <div style={successIconStyle}>✅</div>
          <h3 style={successHeaderStyle}>Password Changed!</h3>
          <p style={successMessageStyle}>
            Your password has been successfully updated.
          </p>
          <p style={instructionStyle}>
            You can now use your new password to log in to your account.
          </p>

          <div style={actionButtonsStyle}>
            <button
              onClick={handleChangeAnother}
              style={secondaryButtonStyle}
            >
              Change Password Again
            </button>
          </div>
        </div>
      )}

      <div style={helpTextStyle}>
        <p>
          <strong>Security Tips:</strong> Choose a strong password with at least 6 characters.
          Include a mix of letters, numbers, and special characters for better security.
        </p>
      </div>
    </div>
  );
}

// Styles
const containerStyle = {
  maxWidth: "500px",
  margin: "0 auto",
  padding: "30px",
  border: "1px solid #ddd",
  borderRadius: "10px",
  backgroundColor: "#f9f9f9",
  boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
  fontFamily: "Arial, sans-serif",
};

const headerStyle = {
  textAlign: "center",
  color: "#333",
  marginBottom: "20px",
  fontSize: "24px",
};

const formContainerStyle = {
  marginBottom: "20px",
};

const instructionStyle = {
  color: "#666",
  marginBottom: "20px",
  lineHeight: "1.5",
  textAlign: "center",
};

const inputGroupStyle = {
  marginBottom: "20px",
};

const labelStyle = {
  display: "block",
  marginBottom: "8px",
  fontWeight: "bold",
  color: "#333",
};

const inputStyle = {
  width: "100%",
  padding: "12px",
  border: "1px solid #ccc",
  borderRadius: "5px",
  fontSize: "16px",
  boxSizing: "border-box",
};

const passwordInputContainerStyle = {
  position: "relative",
  display: "flex",
  alignItems: "center",
};

const passwordInputStyle = {
  width: "100%",
  padding: "12px 50px 12px 12px", // Extra padding on right for eye button
  border: "1px solid #ccc",
  borderRadius: "5px",
  fontSize: "16px",
  boxSizing: "border-box",
};

const eyeButtonStyle = {
  position: "absolute",
  right: "10px",
  background: "none",
  border: "none",
  cursor: "pointer",
  fontSize: "18px",
  padding: "5px",
  borderRadius: "3px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  minWidth: "30px",
  height: "30px",
};

const buttonStyle = {
  width: "100%",
  padding: "12px",
  backgroundColor: "#FFA500",
  color: "white",
  border: "none",
  borderRadius: "5px",
  fontSize: "16px",
  fontWeight: "bold",
  cursor: "pointer",
  transition: "background-color 0.3s",
};

const disabledButtonStyle = {
  backgroundColor: "#ccc",
  cursor: "not-allowed",
};

const secondaryButtonStyle = {
  padding: "10px 20px",
  backgroundColor: "#6c757d",
  color: "white",
  border: "none",
  borderRadius: "5px",
  cursor: "pointer",
  fontSize: "14px",
};

const errorStyle = {
  color: "#dc3545",
  textAlign: "center",
  marginTop: "15px",
  padding: "10px",
  backgroundColor: "#f8d7da",
  border: "1px solid #f5c6cb",
  borderRadius: "5px",
};

const successStyle = {
  color: "#155724",
  textAlign: "center",
  marginTop: "15px",
  padding: "10px",
  backgroundColor: "#d4edda",
  border: "1px solid #c3e6cb",
  borderRadius: "5px",
};

const successContainerStyle = {
  textAlign: "center",
  marginBottom: "20px",
};

const successIconStyle = {
  fontSize: "48px",
  marginBottom: "15px",
};

const successHeaderStyle = {
  color: "#155724",
  marginBottom: "15px",
};

const successMessageStyle = {
  color: "#333",
  marginBottom: "15px",
  fontSize: "16px",
};

const actionButtonsStyle = {
  marginTop: "20px",
};

const helpTextStyle = {
  marginTop: "20px",
  padding: "15px",
  backgroundColor: "#e9ecef",
  borderRadius: "5px",
  fontSize: "14px",
  color: "#6c757d",
};

export default ResetPassword;
