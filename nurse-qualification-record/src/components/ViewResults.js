import React, { useState, useEffect } from "react";
import { firestoreDB, auth } from "../services/firebase";
import { collection, getDocs } from "firebase/firestore";
import { utils, writeFile } from "xlsx";
import { doc, getDoc } from "firebase/firestore";
import { sendPasswordResetEmail } from "firebase/auth";



function ViewResults({ userRole, department, unit }) {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [departments, setDepartments] = useState([]);
  const [units, setUnits] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState(department || "all");
  const [selectedUnit, setSelectedUnit] = useState(unit || "all");
  const [selectedQualification, setSelectedQualification] = useState("all");
  const [filteredInstructors, setFilteredInstructors] = useState(null);
  const [filteredResults, setFilteredResults] = useState([]);
  const [filteredCRMInstructor, setFilteredCRMInstructor] = useState(null);
  const [selectedQualificationType, setSelectedQualificationType] = useState("");
  const [selectedQualificationSubtype, setSelectedQualificationSubtype] = useState("");
  const [selectedQualifications, setSelectedQualifications] = useState([]);
  const [selectedSubtypes, setSelectedSubtypes] = useState([]);
  const [showCheckboxes, setShowCheckboxes] = useState(false);
  const [qualificationSubtypes, setQualificationSubtypes] = useState({});
  const [qualificationTypes, setQualificationTypes] = useState([{ id: "all", name: "ALL Qualifications" }]);
  const [showResetModal, setShowResetModal] = useState(false);
  const [resetMessage, setResetMessage] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [displayedResults, setDisplayedResults] = useState([]);

  const tableStyle = {
    width: "100%",
    borderCollapse: "collapse",
    fontSize: "14px",
    border: "1px solid #ddd",
    borderRadius: "10px",
    overflow: "hidden",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
  };

  const headerRowStyle = {
    backgroundColor: "#007BFF",
    color: "white",
    textAlign: "left",
  };

  const headerCellStyle = {
    padding: "12px",
    fontWeight: "bold",
    borderBottom: "2px solid #ddd",
  };

  const evenRowStyle = {
    backgroundColor: "#f9f9f9",
  };

  const oddRowStyle = {
    backgroundColor: "#ffffff",
  };

  const cellStyle = {
    padding: "10px",
    borderBottom: "1px solid #ddd",
    textAlign: "left",
  };

  const hoverRowStyle = {
    backgroundColor: "#f1f1f1",
  };

  /* ✅ Sub-Table Styles */
  const subTableStyle = {
    width: "100%",
    borderCollapse: "collapse",
    marginTop: "5px",
    backgroundColor: "#f9f9f9",
  };

  const subHeaderRowStyle = {
    backgroundColor: "#ddd",
  };

  const subHeaderCellStyle = {
    padding: "8px",
    fontWeight: "bold",
    borderBottom: "1px solid #aaa",
  };

  const subRowStyle = {
    borderBottom: "1px solid #ddd",
  };

  const subCellStyle = {
    padding: "8px",
  };


  useEffect(() => {
    const fetchDepartments = async () => {
      const querySnapshot = await getDocs(collection(firestoreDB, "departments"));
      const departmentList = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        name: doc.data().name
      }));
      setDepartments([{ id: "all", name: "ALL Departments" }, ...departmentList]);
    };

    const fetchUnits = async () => {
      if (selectedDepartment !== "all") {
        const querySnapshot = await getDocs(collection(firestoreDB, `departments/${selectedDepartment}/units`));
        const unitList = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          name: doc.data().name
        }));
        setUnits([{ id: "all", name: "ALL Units" }, ...unitList]);
      } else {
        setUnits([{ id: "all", name: "ALL Units" }]);
        setSelectedUnit("all"); // Ensure unit filter resets properly
      }
    };

    const fetchQualifications = async () => {
      const querySnapshot = await getDocs(collection(firestoreDB, "qualifications"));
      const types = querySnapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          id: data.type?.toLowerCase().replace(/\s+/g, "_") || doc.id,
          name: data.title || "Unnamed"
        };
      });
      setQualificationTypes([{ id: "all", name: "ALL Qualifications" }, ...types]);
      if (!types.find(q => q.id === selectedQualification)) {
        setSelectedQualification("all");
      }
    };

    fetchDepartments();
    fetchUnits();
    fetchQualifications();
  }, [selectedDepartment]);

  const toggleCheckbox = (option) => {
    setSelectedQualifications((prev) =>
      prev.includes(option)
        ? prev.filter((item) => item !== option)
        : [...prev, option]
    );
  };
  const fetchResults = async () => {
    setLoading(true);
    const querySnapshot = await getDocs(collection(firestoreDB, "employees"));
    let records = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
      // appointmentDate: doc.data().appointmentDate || null,
      // rnAppointmentDate: doc.data().rnAppointmentDate || null,
    }));

    if (selectedQualifications.length > 0) {
      records = records.filter((record) =>
        record.qualifications?.some((qual) => selectedQualifications.includes(qual.subType))
      );
    }

    setResults(records);
    setLoading(false);
  };

  useEffect(() => {
    fetchResults();
  }, [selectedQualifications]);




  // Log the qualificationSubtypes structure
  useEffect(() => {
    console.log("🔥 Qualification Subtypes Structure:", JSON.stringify(qualificationSubtypes, null, 2));
  }, [qualificationSubtypes]);

  // Log the loaded units for debugging
  useEffect(() => {
    console.log("📦 Units loaded:", units);
  }, [units]);


  // Fetch qualification types and subtypes
  useEffect(() => {
    const fetchQualifications = async () => {
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "qualifications"));
        const subtypesMap = {};

        querySnapshot.forEach((doc) => {
          const { type, options } = doc.data();
          if (type && options) {
            subtypesMap[type] = options;
          }
        });

        console.log("🔥 Subtypes Map:", subtypesMap);
        setQualificationSubtypes(subtypesMap);
      } catch (error) {
        console.error("❌ Error fetching qualification subtypes:", error);
      }
    };

    fetchQualifications();
  }, []);
  // Handle checkbox change
  const handleCheckboxChange = (type, option) => {
    setSelectedSubtypes((prev) => {
      const newSubtypes = { ...prev };
      if (!newSubtypes[type]) newSubtypes[type] = [];

      if (newSubtypes[type].includes(option)) {
        newSubtypes[type] = newSubtypes[type].filter((item) => item !== option);
      } else {
        newSubtypes[type].push(option);
      }

      return newSubtypes;
    });
  };

  // Filter records based on selected qualifications
  useEffect(() => {
    const fetchResults = async () => {
      setLoading(true);
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "employees"));
        let records = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          // appointmentDate: doc.data().appointmentDate || null,
          // rnAppointmentDate: doc.data().rnAppointmentDate || null,
          lastUpdated: doc.data().lastUpdated
            ? (doc.data().lastUpdated.toDate
              ? doc.data().lastUpdated.toDate().toLocaleString()
              : new Date(doc.data().lastUpdated.seconds * 1000).toLocaleString())
            : "N/A",
        }));

        // Filter by selected qualifications
        if (selectedQualifications.length > 0) {
          records = records.filter((record) =>
            record.qualifications?.some((qual) =>
              selectedQualifications.includes(qual.type)
            )
          );
        }

        setResults(records);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching results:", error);
        setLoading(false);
      }
    };

    fetchResults();
  }, [selectedQualifications]);

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "departments"));
        const departmentList = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          name: doc.data().name
        }));
        setDepartments([{ id: "all", name: "ALL Departments" }, ...departmentList]);
      } catch (error) {
        console.error("Error fetching departments:", error);
      }
    };

    const fetchUnits = async () => {
      if (selectedDepartment !== "all") {
        try {
          const querySnapshot = await getDocs(collection(firestoreDB, `departments/${selectedDepartment}/units`));
          const unitList = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            name: doc.data().name
          }));
          setUnits([{ id: "all", name: "ALL Units" }, ...unitList]);
        } catch (error) {
          console.error("Error fetching units:", error);
        }
      } else {
        setUnits([{ id: "all", name: "ALL Units" }]);
        setSelectedUnit("all"); // Ensure unit filter resets properly
      }
    };

    const fetchQualifications = async () => {
      try {
        const docRef = doc(firestoreDB, "qualifications", "types");
        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
          const data = docSnap.data();
          setQualificationTypes(data.types || []);
          setQualificationSubtypes(data.subtypes || {});
        }
      } catch (error) {
        console.error("Error fetching qualifications:", error);
      }
    };

    fetchDepartments();
    fetchUnits();
    fetchQualifications();
  }, [selectedDepartment]);

  // Fetch employees with applied filters
  useEffect(() => {
    const fetchResults = async () => {
      setLoading(true);
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "employees"));
        let records = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          // appointmentDate: doc.data().appointmentDate || null,
          // rnAppointmentDate: doc.data().rnAppointmentDate || null,
          lastUpdated: doc.data().lastUpdated?.toDate().toLocaleString() || "N/A"
        }));

        // Filter by department
        if (selectedDepartment !== "all") {
          records = records.filter((record) => record.department === selectedDepartment);
        }

        // Filter by unit
        if (selectedUnit !== "all") {
          records = records.filter((record) => record.unit === selectedUnit);
        }

        // Filter by selected qualification type
        if (selectedQualificationType) {
          records = records.filter((record) =>
            record.qualifications?.some((qual) => qual.type === selectedQualificationType)
          );
        }

        // Filter by selected qualification subtype
        if (selectedQualificationSubtype) {
          records = records.filter((record) =>
            record.qualifications?.some((qual) => qual.subType === selectedQualificationSubtype)
          );
        }

        setResults(records);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching results:", error);
        setLoading(false);
      }
    };

    if (departments.length > 1 && units.length > 0) {
      fetchResults();
    }
  }, [selectedDepartment, selectedUnit, selectedQualification, filteredInstructors, departments, units]);

  // Search filtering effect
  useEffect(() => {
    if (!searchTerm.trim()) {
      setDisplayedResults(results);
    } else {
      const filtered = results.filter((record) => {
        const searchLower = searchTerm.toLowerCase();
        const nameMatch = record.name && record.name.toLowerCase().includes(searchLower);
        const employeeNoMatch = record.employeeNo && record.employeeNo.toLowerCase().includes(searchLower);
        return nameMatch || employeeNoMatch;
      });
      setDisplayedResults(filtered);
    }
  }, [results, searchTerm]);

  //const qualificationTypes = [
  // { id: "all", name: "ALL Qualifications" },
  //  { id: "bachelor", name: "Bachelor" },
  //   { id: "master", name: "Master" },
  //   { id: "phd_other", name: "PHD/Other" },
  //   { id: "specialty_training", name: "Specialty Training" },
  //    { id: "resuscitation_training", name: "Resuscitation Training" },
  //    { id: "ventilator_training", name: "Ventilator Training" },
  ///    { id: "crm_sim_training", name: "CRM/Sim Training" },
  //  ];


  // last update cell style
  const getLastUpdatedCellStyle = (lastUpdated, qualifications) => {
    const baseStyle = {
      padding: "8px 12px",
      borderBottom: "1px solid #ddd",
      textAlign: "center",
      borderRadius: "20px",
      fontSize: "13px",
      fontWeight: "bold",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    };

    if (!lastUpdated) {
      return { ...baseStyle, backgroundColor: "#f0f0f0", color: "#888" }; // Gray if no timestamp
    }

    if (!qualifications || qualifications.length === 0) {
      return { ...baseStyle, backgroundColor: "#fff8e1", color: "#ff9800" }; // Yellow if no qualifications
    }

    // ✅ Convert Firestore Timestamp to JavaScript Date
    let lastUpdatedDate;
    if (lastUpdated?.toDate) {
      lastUpdatedDate = lastUpdated.toDate();
    } else if (lastUpdated?.seconds) {
      lastUpdatedDate = new Date(lastUpdated.seconds * 1000);
    } else {
      lastUpdatedDate = new Date(lastUpdated); // fallback for string-based
    }

    const now = new Date();
    const diffInDays = Math.floor((now - lastUpdatedDate) / (1000 * 60 * 60 * 24));

    return diffInDays > 365
      ? { ...baseStyle, backgroundColor: "#ffebee", color: "#c62828" } // 🔴 Over a year old
      : { ...baseStyle, backgroundColor: "#e0f7fa", color: "#00796b" }; // 🩵 Updated within a year
  };




  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "departments"));
        const departmentList = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          name: doc.data().name
        }));

        setDepartments([{ id: "all", name: "ALL Departments" }, ...departmentList]);
      } catch (error) {
        console.error("Error fetching departments:", error);
      }
    };

    fetchDepartments();
  }, []);

  useEffect(() => {
    const fetchUnits = async () => {
      if (selectedDepartment !== "all") {
        try {
          const querySnapshot = await getDocs(collection(firestoreDB, `departments/${selectedDepartment}/units`));
          const unitList = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            name: doc.data().name
          }));

          setUnits([{ id: "all", name: "ALL Units" }, ...unitList]);
        } catch (error) {
          console.error("Error fetching units:", error);
        }
      } else {
        setUnits([{ id: "all", name: "ALL Units" }]);
        setSelectedUnit("all"); // Ensure unit filter resets properly
      }
    };

    fetchUnits();
  }, [selectedDepartment]);

  useEffect(() => {
    const fetchResults = async () => {
      setLoading(true);
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "employees"));
        let records = querySnapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            departmentName: departments.find(d => d.id === data.department)?.name || data.department || "N/A",
            unitName: units.find(u => u.id === data.unit)?.name || data.unit || "N/A",
            rawLastUpdated: data.lastUpdated || null,
lastUpdated: data.lastUpdated
  ? (data.lastUpdated.toDate
      ? data.lastUpdated.toDate().toLocaleString()
      : new Date(data.lastUpdated.seconds * 1000).toLocaleString())
  : "N/A",
          };
        });

        // **Restrict by User Role **
        if (userRole === "department") {
          records = records.filter((record) => record.department === department);
        } else if (userRole === "unit") {
          records = records.filter((record) => record.unit === unit);
        }

        // **Filter by Department**
        if (selectedDepartment !== "all") {
          records = records.filter((record) => record.department === selectedDepartment);
        }

        // **Filter by Unit**
        if (selectedUnit && selectedUnit !== "all") {
          const selectedUnitName = units.find(u => u.id === selectedUnit)?.name;
          if (selectedUnitName) {
            records = records.filter((record) => record.unit === selectedUnitName);
          }
        }

        // **Filter by Qualification Type**
        if (selectedQualification !== "all") {
          records = records
            .map((record) => ({
              ...record,
              qualifications: record.qualifications?.filter(
                (qual) =>
                  qual.type &&
                  qual.type.toLowerCase().replace(/\s+/g, "_") === selectedQualification
              ),
            }))
            .filter((record) => record.qualifications && record.qualifications.length > 0);
        }

        // Sort qualifications alphabetically by type, then by subType
        records = records.map((record) => ({
          ...record,
          qualifications: record.qualifications?.sort((a, b) => {
            const typeA = a.type?.toLowerCase() || "";
            const typeB = b.type?.toLowerCase() || "";
            const subTypeA = a.subType?.toLowerCase() || "";
            const subTypeB = b.subType?.toLowerCase() || "";

            if (typeA < typeB) return -1;
            if (typeA > typeB) return 1;
            return subTypeA.localeCompare(subTypeB);
          }) || [],
        }));

        // **Filter for BLS Instructor / AED 123 Instructor**
        if (filteredInstructors) {
          records = records.filter((record) =>
            record.qualifications?.some(
              (qual) =>
                qual.type === "Resuscitation Training" &&
                qual.subType === filteredInstructors
            )
          );
        }



        setResults(records);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching results:", error);
        setLoading(false);
      }
    };

    if (departments.length > 1 && units.length > 0) {
      fetchResults();
    }
  }, [selectedDepartment, selectedUnit, selectedQualification, filteredInstructors, departments, units]);





  













  const exportRawToExcel = () => {
  const MAX_QUALIFICATIONS = 10;

  const flattenedRaw = displayedResults.map((record) => {
    let lastUpdatedStr = "N/A";
    if (record.lastUpdated && record.lastUpdated.seconds) {
      lastUpdatedStr = new Date(record.lastUpdated.seconds * 1000).toLocaleString();
    }
 
    const qualificationData = {};
    const qualifications = record.qualifications || [];
 
    for (let i = 0; i < MAX_QUALIFICATIONS; i++) {
      const qual = qualifications[i] || {};
      qualificationData[`Qualification_${i + 1}_Type`] = qual.type || "N/A";
      qualificationData[`Qualification_${i + 1}_SubType`] = qual.subType || "N/A";
      qualificationData[`Qualification_${i + 1}_CourseTitle`] = qual.courseTitle || "N/A";
      qualificationData[`Qualification_${i + 1}_FellowshipTitle`] = qual.fellowshipTitle || "N/A";
      qualificationData[`Qualification_${i + 1}_YearObtained`] = qual.yearObtained || "N/A";
    }
 
    return {
      EmployeeNo: record.employeeNo || "N/A",
      Name: record.name || "N/A",
      Department: record.department || "N/A",
      Unit: record.unit || "N/A",
      Rank: record.rank || "N/A",
      GraduatedThisYear: record.freshGrad || "N/A", 
      LastUpdated: lastUpdatedStr,
      ...qualificationData,
      // "Date of Present Rank Appointment": record.appointmentDate || "N/A",
      // "Date of RN Appointment in HA": record.rnAppointmentDate || "N/A",
    };
  });
 
  const worksheet = utils.json_to_sheet(flattenedRaw);
  const workbook = utils.book_new();
  utils.book_append_sheet(workbook, worksheet, "Raw Records");
  writeFile(workbook, "Nurse_Training_Raw_Data.xlsx");
  };
  const exportToExcel = () => {
  // 1. Gather all unique qualification fields
  const allQualificationFields = new Set();
  displayedResults.forEach(record => {
    (record.qualifications || []).forEach(qual => {
      const field = `${qual.type} - ${qual.subType}`;
      allQualificationFields.add(field);
    });
  });

  // Convert to array and sort alphabetically
  const qualificationColumns = Array.from(allQualificationFields).sort();

  // 2. Flatten the records using standardized columns
  const flattenedResults = displayedResults.map((record) => {
    let lastUpdatedStr = "N/A";
    if (record.lastUpdated && record.lastUpdated.seconds) {
      lastUpdatedStr = new Date(record.lastUpdated.seconds * 1000).toLocaleString();
    }

    // Create a map of this user's latest qualifications
    const qualMap = new Map();
    (record.qualifications || []).forEach(qual => {
      const key = `${qual.type} - ${qual.subType}`;
      const existing = qualMap.get(key);
      if (!existing || (qual.yearObtained && (!existing.yearObtained || parseInt(qual.yearObtained) > parseInt(existing.yearObtained)))) {
        qualMap.set(key, qual);
      }
    });

    // Build row with all standard fields using yearObtained
    const qualificationData = {};
    qualificationColumns.forEach(field => {
      const qual = qualMap.get(field);
      qualificationData[field] = qual?.yearObtained || "N/A";
    });

    return {
      EmployeeNo: record.employeeNo || "N/A",
      Name: record.name || "N/A",
      Department: record.department || "N/A",
      Unit: record.unit || "N/A",
      Rank: record.rank || "N/A",
      GraduatedThisYear: record.freshGrad || "N/A", 
      LastUpdated: lastUpdatedStr,
      ...qualificationData,
      // "Date of Present Rank Appointment": record.appointmentDate || "N/A",
      // "Date of RN Appointment in HA": record.rnAppointmentDate || "N/A",
    };
  });

    // ✅ Convert JSON to Excel format
    const worksheet = utils.json_to_sheet(flattenedResults);
    const workbook = utils.book_new();
    utils.book_append_sheet(workbook, worksheet, "Employee Records");

    // ✅ Save the Excel file
    writeFile(workbook, "Nurse_Training_Records.xlsx");
  };

  if (loading) {
    return <p>Loading results...</p>;
  }





  return (









    <div style={{ padding: "20px", maxWidth: "100%", margin: "0 auto", fontFamily: "Arial, sans-serif" }}>
      <h2 style={{ textAlign: "center", marginBottom: "20px" }}>Filtered Training Records</h2>

      {/* Search Input */}
      <div style={{ marginBottom: "20px" }}>
        <input
          type="text"
          placeholder="Search by Name or Employee ID..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{
            width: "100%",
            maxWidth: "400px",
            padding: "10px",
            fontSize: "16px",
            border: "2px solid #007BFF",
            borderRadius: "25px",
            outline: "none",
            boxSizing: "border-box",
            marginBottom: "10px"
          }}
        />
        {searchTerm.trim() && (
          <p style={{
            fontSize: "14px",
            color: "#666",
            margin: "5px 0 0 0",
            fontStyle: "italic"
          }}>
            Showing {displayedResults.length} result{displayedResults.length !== 1 ? 's' : ''} for "{searchTerm}"
          </p>
        )}
      </div>

      <div style={{ display: "flex", gap: "10px", marginBottom: "20px", flexWrap: "wrap" }}>
        <div>
          <label><strong>Department:</strong></label>
          <select
            value={selectedDepartment}
            onChange={(e) => setSelectedDepartment(e.target.value)}
            style={{ width: "200px", padding: "8px", borderRadius: "5px" }}
          >
            {departments.map((dept) => (
              <option key={dept.id} value={dept.id}>{dept.name}</option>
            ))}
          </select>
        </div>

        <div>
          <label><strong>Unit:</strong></label>
          <select
            value={selectedUnit}
            onChange={(e) => setSelectedUnit(e.target.value)}
            style={{ width: "200px", padding: "8px", borderRadius: "5px" }}
          >
            {units.map((unit) => (
              <option key={unit.id} value={unit.id}>{unit.name}</option>
            ))}
          </select>
        </div>

        <div>
          <label><strong>Filter by Qualification Type:</strong></label>
          <select
            value={selectedQualification}
            onChange={(e) => setSelectedQualification(e.target.value)}
            style={{ width: "250px", padding: "8px", borderRadius: "5px" }}
          >
            {
              !qualificationTypes.some(q => q.id === selectedQualification) &&
              <option value={selectedQualification}>{selectedQualification}</option>
            }
            {qualificationTypes.map((qual) => (
              <option key={qual.id} value={qual.id}>{qual.name}</option>
            ))}
          </select>

        </div>


        <button
          onClick={exportToExcel}
          style={{
            padding: "8px 16px",
            backgroundColor: "#007BFF",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
            alignSelf: "center"
          }}
        >
          Export to Excel (Year Obtain only)
        </button>
        <button
          onClick={exportRawToExcel}
          style={{
            padding: "8px 16px",
            backgroundColor: "#6c757d",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
            alignSelf: "center"
          }}
        >
          Export to Excel (Raw Data)
        </button>
      

       
      </div>

     

     

      {displayedResults.length === 0 ? (
        <p style={{ textAlign: "center", fontSize: "18px", color: "red" }}>
          {searchTerm.trim() ? `No records found matching "${searchTerm}"` : "No records found."}
        </p>
      ) : (
        <table style={tableStyle}>
          <thead>
            <tr style={headerRowStyle}>
              <th style={headerCellStyle}>Employee No</th><th style={headerCellStyle}>Name</th>
              <th style={headerCellStyle}>Department</th><th style={headerCellStyle}>Unit</th>
              <th style={headerCellStyle}>Rank</th><th style={headerCellStyle}>Qualifications</th>
              <th style={headerCellStyle}>Last Updated</th> {/* ✅ Added Column */}
              <th style={headerCellStyle}>Graduated This Year</th>
              {/* <th style={headerCellStyle}>Year of Present Rank Appointment</th>
              <th style={headerCellStyle}>Year of RN Appointment in HA</th> */}
            </tr>
          </thead>
          <tbody>
            {displayedResults.map((record, index) => (
              <tr key={record.id} style={index % 2 === 0 ? evenRowStyle : oddRowStyle}>
                <td style={cellStyle}>{record.employeeNo || "N/A"}</td><td style={cellStyle}>{record.name || "N/A"}</td>
                <td style={cellStyle}>{record.departmentName || "N/A"}</td><td style={cellStyle}>{record.unitName || "N/A"}</td>
                <td style={cellStyle}>{record.rank || "N/A"}</td>
                <td style={cellStyle}>
                  {record.qualifications && record.qualifications.length > 0 ? (
                    <table style={subTableStyle}>
                      <thead>
                        <tr style={subHeaderRowStyle}>
                          <th style={subHeaderCellStyle}>Type</th><th style={subHeaderCellStyle}>SubType</th>
                          <th style={subHeaderCellStyle}>Course Title</th><th style={subHeaderCellStyle}>Year Obtained</th>
                        </tr>
                      </thead>
                      <tbody>
                        {record.qualifications.map((qual, idx) => (
                          <tr key={idx} style={subRowStyle}>
                            <td style={subCellStyle}>{qualificationTypes.find(q => q.id === qual.type?.toLowerCase().replace(/\s+/g, "_"))?.name || qual.type || "N/A"}</td><td style={subCellStyle}>{qual.subType || "N/A"}</td>
                            <td style={subCellStyle}>{qual.fellowshipTitle || qual.courseTitle || "N/A"}</td><td style={subCellStyle}>{qual.yearObtained || "N/A"}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : "N/A"}
                </td>
                <td style={getLastUpdatedCellStyle(record.rawLastUpdated, record.qualifications)}>
                  {record.lastUpdated || "N/A"}
                </td>
                <td style={cellStyle}>
  {record.freshGrad ? record.freshGrad : "N/A"}
</td>
                {/* <td style={cellStyle}>
                  {record.appointmentDate && typeof record.appointmentDate === "string"
                      ? record.appointmentDate
                      : "N/A"}
                </td>
                <td style={cellStyle}>
                  {record.rnAppointmentDate && typeof record.rnAppointmentDate === "string"
                      ? record.rnAppointmentDate
                      : "N/A"}
                </td> */}
              </tr>
            ))}
          </tbody>
        </table>
      )}


    </div>





  );
}

export default ViewResults;
