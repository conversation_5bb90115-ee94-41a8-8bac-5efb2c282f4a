import React, { useState, useEffect } from "react";
import { firestoreDB, auth } from "../services/firebase";
import { collection, doc, setDoc, getDocs } from "firebase/firestore";
import { createUserWithEmailAndPassword } from "firebase/auth";

function AddUsers() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");  
  const [role, setRole] = useState("unit"); // Default role
  const [department, setDepartment] = useState("");
  const [unit, setUnit] = useState("");
  const [selectedDepartments, setSelectedDepartments] = useState([]);
  const [selectedUnits, setSelectedUnits] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [units, setUnits] = useState([]);
  const [allUnits, setAllUnits] = useState([]); // Store all units for multi-admin
  const [message, setMessage] = useState("");

  // Fetch Departments from Firestore
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "departments"));
        const departmentList = querySnapshot.docs
          .filter((doc) => doc.id !== "archived_data") // Exclude archived_data
          .map((doc) => ({
            id: doc.id, // Keep department ID
            name: doc.data().name // Store readable department name
          }));
        setDepartments(departmentList);
      } catch (error) {
        console.error("Error fetching departments:", error);
      }
    };
    fetchDepartments();
  }, []);

  // Fetch all units for multi-admin functionality
  useEffect(() => {
    const fetchAllUnits = async () => {
      try {
        const allUnitsData = [];
        for (const dept of departments) {
          const querySnapshot = await getDocs(collection(firestoreDB, `departments/${dept.id}/units`));
          const unitList = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            name: doc.data().name,
            departmentId: dept.id,
            departmentName: dept.name
          }));
          allUnitsData.push(...unitList);
        }
        setAllUnits(allUnitsData);
      } catch (error) {
        console.error("Error fetching all units:", error);
      }
    };

    if (departments.length > 0) {
      fetchAllUnits();
    }
  }, [departments]);

  // Fetch Units based on selected Department
  useEffect(() => {
    const fetchUnits = async () => {
      if (department) {
        try {
          const querySnapshot = await getDocs(collection(firestoreDB, `departments/${department}/units`));
          const unitList = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            name: doc.data().name // Store readable unit name (e.g., "Ward 2B")
          }));
          setUnits(unitList);
        } catch (error) {
          console.error("Error fetching units:", error);
        }
      } else {
        setUnits([]); // Reset units if no department is selected
      }
    };
    fetchUnits();
  }, [department]);

  // Handle multiple department selection
  const handleDepartmentToggle = (deptId) => {
    setSelectedDepartments(prev =>
      prev.includes(deptId)
        ? prev.filter(id => id !== deptId)
        : [...prev, deptId]
    );
  };

  // Handle multiple unit selection
  const handleUnitToggle = (unitId) => {
    setSelectedUnits(prev =>
      prev.includes(unitId)
        ? prev.filter(id => id !== unitId)
        : [...prev, unitId]
    );
  };

  // Clear selections when role changes
  const handleRoleChange = (newRole) => {
    setRole(newRole);
    setDepartment("");
    setUnit("");
    setSelectedDepartments([]);
    setSelectedUnits([]);
  };

  const handleAddUser = async () => {
    if (!email || !password || !role) {
      setMessage("All fields are required.");
      return;
    }

    try {
      // Create user in Firebase Authentication
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const userId = userCredential.user.uid;

      // Define Firestore user data
      let userData = { email, role };

      if (role === "admin") {
        // Admin has full system access - no specific departments/units needed
        // Admin role doesn't need any additional fields
      } else if (role === "department") {
        // Department role can have multiple departments
        if (selectedDepartments.length === 0) {
          setMessage("Please select at least one department.");
          return;
        }
        userData.departments = selectedDepartments; // Array of department IDs
      } else if (role === "unit") {
        // Unit role can have multiple units
        if (selectedUnits.length === 0) {
          setMessage("Please select at least one unit.");
          return;
        }
        userData.units = selectedUnits; // Array of unit IDs with department info
      }

      // Save user details in Firestore with UID as document ID
      await setDoc(doc(firestoreDB, "users", userId), userData);

      setMessage("User added successfully!");
      setEmail("");
      setPassword("");
      setRole("unit");
      setDepartment("");
      setUnit("");
      setSelectedDepartments([]);
      setSelectedUnits([]);
    } catch (error) {
      console.error("Error adding user:", error);
      setMessage("Failed to add user.");
    }
  };

  return (
    <div style={{ padding: "20px", maxWidth: "500px", margin: "0 auto" }}>
      <h2>Add New User</h2>

      <label>Email:</label>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Enter email"
        style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}
      />

      <label>Password:</label>
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        placeholder="Enter password"
        style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}
      />

      <label>Role:</label>
      <select value={role} onChange={(e) => handleRoleChange(e.target.value)}
        style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}>
        <option value="admin">Admin (Full System Access)</option>
        <option value="department">Department (Multiple Departments)</option>
        <option value="unit">Unit (Multiple Units)</option>
      </select>

      {role === "department" && (
        <div style={{ marginBottom: "20px" }}>
          <h3 style={{ marginBottom: "15px", color: "#007BFF" }}>Department Access Control</h3>
          <p style={{ fontSize: "14px", color: "#666", marginBottom: "15px" }}>
            Select multiple departments this user can manage.
          </p>

          <div style={{ marginBottom: "15px" }}>
            <label style={{ fontWeight: "bold", display: "block", marginBottom: "8px" }}>
              Departments ({selectedDepartments.length} selected):
            </label>
            <div style={{
              border: "1px solid #ddd",
              borderRadius: "5px",
              padding: "10px",
              maxHeight: "150px",
              overflowY: "auto",
              backgroundColor: "#f9f9f9"
            }}>
              {departments.map((dept) => (
                <label key={dept.id} style={{
                  display: "block",
                  marginBottom: "5px",
                  cursor: "pointer",
                  padding: "5px",
                  borderRadius: "3px",
                  backgroundColor: selectedDepartments.includes(dept.id) ? "#e3f2fd" : "transparent"
                }}>
                  <input
                    type="checkbox"
                    checked={selectedDepartments.includes(dept.id)}
                    onChange={() => handleDepartmentToggle(dept.id)}
                    style={{ marginRight: "8px" }}
                  />
                  {dept.name}
                </label>
              ))}
            </div>
          </div>

          {selectedDepartments.length > 0 ? (
            <div style={{
              padding: "10px",
              backgroundColor: "#d4edda",
              border: "1px solid #c3e6cb",
              borderRadius: "5px",
              fontSize: "14px"
            }}>
              <strong>Selected Departments:</strong>
              <ul style={{ margin: "5px 0", paddingLeft: "20px" }}>
                <li>{selectedDepartments.map(id =>
                  departments.find(d => d.id === id)?.name
                ).join(", ")}</li>
              </ul>
            </div>
          ) : (
            <div style={{
              padding: "10px",
              backgroundColor: "#fff3cd",
              border: "1px solid #ffeaa7",
              borderRadius: "5px",
              fontSize: "14px"
            }}>
              ⚠️ Please select at least one department.
            </div>
          )}
        </div>
      )}

      {role === "unit" && (
        <div style={{ marginBottom: "20px" }}>
          <h3 style={{ marginBottom: "15px", color: "#007BFF" }}>Unit Access Control</h3>
          <p style={{ fontSize: "14px", color: "#666", marginBottom: "15px" }}>
            Select multiple units this user can manage across different departments.
          </p>

          <div style={{ marginBottom: "15px" }}>
            <label style={{ fontWeight: "bold", display: "block", marginBottom: "8px" }}>
              Units ({selectedUnits.length} selected):
            </label>
            <div style={{
              border: "1px solid #ddd",
              borderRadius: "5px",
              padding: "10px",
              maxHeight: "200px",
              overflowY: "auto",
              backgroundColor: "#f9f9f9"
            }}>
              {allUnits.map((unit) => (
                <label key={`${unit.departmentId}-${unit.id}`} style={{
                  display: "block",
                  marginBottom: "5px",
                  cursor: "pointer",
                  padding: "5px",
                  borderRadius: "3px",
                  backgroundColor: selectedUnits.includes(`${unit.departmentId}-${unit.id}`) ? "#e8f5e8" : "transparent"
                }}>
                  <input
                    type="checkbox"
                    checked={selectedUnits.includes(`${unit.departmentId}-${unit.id}`)}
                    onChange={() => handleUnitToggle(`${unit.departmentId}-${unit.id}`)}
                    style={{ marginRight: "8px" }}
                  />
                  <span style={{ fontWeight: "bold", color: "#666" }}>{unit.departmentName}</span> → {unit.name}
                </label>
              ))}
            </div>
          </div>

          {selectedUnits.length > 0 ? (
            <div style={{
              padding: "10px",
              backgroundColor: "#d4edda",
              border: "1px solid #c3e6cb",
              borderRadius: "5px",
              fontSize: "14px"
            }}>
              <strong>Selected Units:</strong>
              <ul style={{ margin: "5px 0", paddingLeft: "20px" }}>
                <li>{selectedUnits.map(unitKey => {
                  const unit = allUnits.find(u => `${u.departmentId}-${u.id}` === unitKey);
                  return unit ? `${unit.departmentName} → ${unit.name}` : unitKey;
                }).join(", ")}</li>
              </ul>
            </div>
          ) : (
            <div style={{
              padding: "10px",
              backgroundColor: "#fff3cd",
              border: "1px solid #ffeaa7",
              borderRadius: "5px",
              fontSize: "14px"
            }}>
              ⚠️ Please select at least one unit.
            </div>
          )}
        </div>
      )}

      {/* Legacy single selections - keeping for backward compatibility */}
      {role === "department_single" && (
        <>
          <label>Department:</label>
          <select value={department} onChange={(e) => setDepartment(e.target.value)}
            style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}>
            <option value="">-- Select Department --</option>
            {departments.map((dept) => (
              <option key={dept.id} value={dept.id}>{dept.name}</option>
            ))}
          </select>
        </>
      )}

      {role === "unit" && (
        <>
          <label>Department:</label>
          <select value={department} onChange={(e) => setDepartment(e.target.value)}
            style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}>
            <option value="">-- Select Department --</option>
            {departments.map((dept) => (
              <option key={dept.id} value={dept.id}>{dept.name}</option>
            ))}
          </select>

          <label>Unit:</label>
          <select value={unit} onChange={(e) => setUnit(e.target.value)}
            style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}>
            <option value="">-- Select Unit --</option>
            {units.map((unit) => (
              <option key={unit.id} value={unit.name}>{unit.name}</option> // Stores readable name
            ))}
          </select>
        </>
      )}

      <button onClick={handleAddUser} style={{
        padding: "10px",
        backgroundColor: "#007BFF",
        color: "white",
        border: "none",
        borderRadius: "5px",
        width: "100%",
        marginTop: "10px"
      }}>
        Add User
      </button>

      {message && <p style={{ color: message.includes("successfully") ? "green" : "red" }}>{message}</p>}
    </div>
  );
}

export default AddUsers;