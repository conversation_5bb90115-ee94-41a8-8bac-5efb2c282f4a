import React, { useState, useEffect } from "react";
import { firestoreDB, auth } from "../services/firebase";
import { collection, doc, setDoc, getDocs } from "firebase/firestore";
import { createUserWithEmailAndPassword } from "firebase/auth";

function AddUsers() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");  
  const [role, setRole] = useState("unit"); // Default role
  const [department, setDepartment] = useState("");
  const [unit, setUnit] = useState("");
  const [departments, setDepartments] = useState([]);
  const [units, setUnits] = useState([]);
  const [message, setMessage] = useState("");

  // Fetch Departments from Firestore
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "departments"));
        const departmentList = querySnapshot.docs.map((doc) => ({
          id: doc.id, // Keep department ID
          name: doc.data().name // Store readable department name
        }));
        setDepartments(departmentList);
      } catch (error) {
        console.error("Error fetching departments:", error);
      }
    };
    fetchDepartments();
  }, []);

  // Fetch Units based on selected Department
  useEffect(() => {
    const fetchUnits = async () => {
      if (department) {
        try {
          const querySnapshot = await getDocs(collection(firestoreDB, `departments/${department}/units`));
          const unitList = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            name: doc.data().name // Store readable unit name (e.g., "Ward 2B")
          }));
          setUnits(unitList);
        } catch (error) {
          console.error("Error fetching units:", error);
        }
      } else {
        setUnits([]); // Reset units if no department is selected
      }
    };
    fetchUnits();
  }, [department]);

  const handleAddUser = async () => {
    if (!email || !password || !role) {
      setMessage("All fields are required.");
      return;
    }

    try {
      // Create user in Firebase Authentication
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const userId = userCredential.user.uid;

      // Define Firestore user data
      let userData = { email, role };
      
      if (role === "department") {
        if (!department) {
          setMessage("Please select a department.");
          return;
        }
        userData.department = department; // Store department ID
      } 
      
      if (role === "unit") {
        if (!department || !unit) {
          setMessage("Please select a department and unit.");
          return;
        }
        userData.department = department; // Store department ID
        userData.unit = unit; // Store readable unit name (e.g., "Ward 2B")
      }

      // Save user details in Firestore with UID as document ID
      await setDoc(doc(firestoreDB, "users", userId), userData);

      setMessage("User added successfully!");
      setEmail("");
      setPassword("");
      setRole("unit");
      setDepartment("");
      setUnit("");
    } catch (error) {
      console.error("Error adding user:", error);
      setMessage("Failed to add user.");
    }
  };

  return (
    <div style={{ padding: "20px", maxWidth: "500px", margin: "0 auto" }}>
      <h2>Add New User</h2>

      <label>Email:</label>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Enter email"
        style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}
      />

      <label>Password:</label>
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        placeholder="Enter password"
        style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}
      />

      <label>Role:</label>
      <select value={role} onChange={(e) => setRole(e.target.value)}
        style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}>
        <option value="admin">Admin</option>
        <option value="department">Department</option>
        <option value="unit">Unit</option>
      </select>

      {role === "department" && (
        <>
          <label>Department:</label>
          <select value={department} onChange={(e) => setDepartment(e.target.value)}
            style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}>
            <option value="">-- Select Department --</option>
            {departments.map((dept) => (
              <option key={dept.id} value={dept.id}>{dept.name}</option>
            ))}
          </select>
        </>
      )}

      {role === "unit" && (
        <>
          <label>Department:</label>
          <select value={department} onChange={(e) => setDepartment(e.target.value)}
            style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}>
            <option value="">-- Select Department --</option>
            {departments.map((dept) => (
              <option key={dept.id} value={dept.id}>{dept.name}</option>
            ))}
          </select>

          <label>Unit:</label>
          <select value={unit} onChange={(e) => setUnit(e.target.value)}
            style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}>
            <option value="">-- Select Unit --</option>
            {units.map((unit) => (
              <option key={unit.id} value={unit.name}>{unit.name}</option> // Stores readable name
            ))}
          </select>
        </>
      )}

      <button onClick={handleAddUser} style={{
        padding: "10px",
        backgroundColor: "#007BFF",
        color: "white",
        border: "none",
        borderRadius: "5px",
        width: "100%",
        marginTop: "10px"
      }}>
        Add User
      </button>

      {message && <p style={{ color: message.includes("successfully") ? "green" : "red" }}>{message}</p>}
    </div>
  );
}

export default AddUsers;