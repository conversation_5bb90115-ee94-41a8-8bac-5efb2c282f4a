import React, { useState, useEffect } from "react";
import { firestoreDB } from "../services/firebase";
import { collection, getDocs } from "firebase/firestore";

function UserList({ userRole, userDepartment, userUnit }) {
  const [users, setUsers] = useState([]);
  const [search, setSearch] = useState("");
  const [filteredUsers, setFilteredUsers] = useState([]);

  // Fetch users from Firestore
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "users"));
        let fetchedUsers = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));

        // Role-based filtering
        if (userRole === "department") {
          fetchedUsers = fetchedUsers.filter(
            (user) => user.department === userDepartment
          );
        } else if (userRole === "unit") {
          fetchedUsers = fetchedUsers.filter((user) => user.unit === userUnit);
        }

        setUsers(fetchedUsers);
        setFilteredUsers(fetchedUsers);
      } catch (error) {
        console.error("Error fetching users:", error);
      }
    };

    fetchUsers();
  }, [userRole, userDepartment, userUnit]);

  // Handle search
  useEffect(() => {
    const lowercasedSearch = search.toLowerCase();
    setFilteredUsers(
      users.filter(
        (user) =>
          (user.email && user.email.toLowerCase().includes(lowercasedSearch)) ||
          (user.department && user.department.toLowerCase().includes(lowercasedSearch)) ||
          (user.unit && user.unit.toLowerCase().includes(lowercasedSearch)) ||
          (user.role && user.role.toLowerCase().includes(lowercasedSearch))
      )
    );
  }, [search, users]);

  return (
    <div style={containerStyle}>
      <h2 style={{ textAlign: "center" }}>User List</h2>

      <input
        type="text"
        placeholder="Search by Email, Department, or Unit..."
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        style={searchInputStyle}
      />

      {filteredUsers.length === 0 ? (
        <p style={{ textAlign: "center", color: "red" }}>No users found.</p>
      ) : (
        <table style={tableStyle}>
          <thead>
            <tr style={tableHeaderStyle}>
              <th style={tableCellStyle}>Email</th>
              <th style={tableCellStyle}>Role</th>
              <th style={tableCellStyle}>Department</th>
              <th style={tableCellStyle}>Unit</th>
            </tr>
          </thead>
          <tbody>
            {filteredUsers.map((user) => (
              <tr key={user.id} style={tableRowStyle}>
                <td style={tableCellStyle}>{user.email || "N/A"}</td>
                <td style={tableCellStyle}>{user.role || "N/A"}</td>
                <td style={tableCellStyle}>{user.department || "N/A"}</td>
                <td style={tableCellStyle}>{user.unit || "N/A"}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
}

// Styles
const containerStyle = {
  padding: "20px",
  maxWidth: "800px",
  margin: "0 auto",
  fontFamily: "Arial, sans-serif",
};

const searchInputStyle = {
  width: "100%",
  padding: "10px",
  marginBottom: "15px",
  borderRadius: "5px",
  border: "1px solid #ccc",
};

const tableStyle = {
  width: "100%",
  borderCollapse: "collapse",
  textAlign: "left",
  border: "1px solid #ddd",
};

const tableHeaderStyle = {
  backgroundColor: "#f4f4f4",
  fontWeight: "bold",
  textAlign: "left",
  padding: "12px",
  borderBottom: "2px solid #ddd",
};

const tableCellStyle = {
  padding: "10px",
  borderBottom: "1px solid #ddd",
};

const tableRowStyle = {
  backgroundColor: "#fff",
};

export default UserList;