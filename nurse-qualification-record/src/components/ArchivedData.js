import React, { useState, useEffect } from "react";
import { firestoreDB } from "../services/firebase";
import { collection, getDocs, doc, setDoc, deleteDoc } from "firebase/firestore";
import { utils, writeFile } from "xlsx";

function ArchivedData({ userRole }) {
  const [archivedRecords, setArchivedRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [displayedResults, setDisplayedResults] = useState([]);

  // Fetch archived records
  useEffect(() => {
    // Only fetch if user is admin
    if (userRole !== "admin") {
      setLoading(false);
      return;
    }
    const fetchArchivedRecords = async () => {
      setLoading(true);
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "archived_data"));
        const records = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          archivedAt: doc.data().archivedAt?.toDate().toLocaleString() || "N/A"
        }));
        setArchivedRecords(records);
        setDisplayedResults(records);
      } catch (error) {
        console.error("Error fetching archived records:", error);
      }
      setLoading(false);
    };

    fetchArchivedRecords();
  }, [userRole]);

  // Search filtering
  useEffect(() => {
    // Only filter if user is admin
    if (userRole !== "admin") {
      return;
    }

    if (!searchTerm.trim()) {
      setDisplayedResults(archivedRecords);
    } else {
      const filtered = archivedRecords.filter((record) => {
        const searchLower = searchTerm.toLowerCase();
        const nameMatch = record.name && record.name.toLowerCase().includes(searchLower);
        const employeeNoMatch = record.employeeNo && record.employeeNo.toLowerCase().includes(searchLower);
        return nameMatch || employeeNoMatch;
      });
      setDisplayedResults(filtered);
    }
  }, [archivedRecords, searchTerm, userRole]);

  // Restore employee function
  const restoreEmployee = async (recordId, recordData) => {
    const confirmRestore = window.confirm(
      `🔄 Restore Employee Record\n\n` +
      `Employee: ${recordData.name} (${recordData.employeeNo})\n` +
      `Archived: ${recordData.archivedAt}\n` +
      `Archived by: ${recordData.archivedBy}\n\n` +
      `This will move the record back to active employees.\n\n` +
      `Are you sure you want to restore this employee?`
    );

    if (!confirmRestore) return;

    try {
      // Remove archive-specific fields
      const { archivedAt, archivedBy, originalId, ...employeeData } = recordData;

      // Restore to employees collection
      const employeeRef = doc(firestoreDB, "employees", recordData.originalId || recordId);
      await setDoc(employeeRef, employeeData);

      // Delete from archived_data collection
      const archiveRef = doc(firestoreDB, "archived_data", recordId);
      await deleteDoc(archiveRef);

      // Update local state
      const updatedRecords = archivedRecords.filter(record => record.id !== recordId);
      setArchivedRecords(updatedRecords);
      setDisplayedResults(displayedResults.filter(record => record.id !== recordId));

      alert(`✅ Employee Restored Successfully!\n\n${recordData.name} (${recordData.employeeNo}) has been restored to active employees.`);

    } catch (error) {
      console.error("Error restoring employee:", error);
      alert(`❌ Restore Failed!\n\nError: ${error.message}\n\nPlease try again.`);
    }
  };

  // Permanently delete function
  const permanentlyDelete = async (recordId, recordData) => {
    const confirmDelete = window.confirm(
      `⚠️ PERMANENT DELETE WARNING\n\n` +
      `Employee: ${recordData.name} (${recordData.employeeNo})\n` +
      `Archived: ${recordData.archivedAt}\n\n` +
      `This will PERMANENTLY DELETE the employee record.\n` +
      `This action CANNOT be undone!\n\n` +
      `Type "DELETE" to confirm:`
    );

    if (!confirmDelete) return;

    const confirmText = prompt(
      `To confirm permanent deletion, type "DELETE" (all caps):`
    );

    if (confirmText !== "DELETE") {
      alert("❌ Deletion cancelled. Confirmation text did not match.");
      return;
    }

    try {
      // Delete from archived_data collection
      const archiveRef = doc(firestoreDB, "archived_data", recordId);
      await deleteDoc(archiveRef);

      // Update local state
      const updatedRecords = archivedRecords.filter(record => record.id !== recordId);
      setArchivedRecords(updatedRecords);
      setDisplayedResults(displayedResults.filter(record => record.id !== recordId));

      alert(`✅ Employee Permanently Deleted!\n\n${recordData.name} (${recordData.employeeNo}) has been permanently removed from the system.`);

    } catch (error) {
      console.error("Error permanently deleting employee:", error);
      alert(`❌ Delete Failed!\n\nError: ${error.message}\n\nPlease try again.`);
    }
  };

  // Export archived data
  const exportArchivedData = () => {
    if (displayedResults.length === 0) {
      alert("No archived data to export.");
      return;
    }

    const exportData = displayedResults.map(record => ({
      EmployeeNo: record.employeeNo || "N/A",
      Name: record.name || "N/A",
      Department: record.departmentName || record.department || "N/A",
      Unit: record.unitName || record.unit || "N/A",
      Rank: record.rank || "N/A",
      ArchivedAt: record.archivedAt || "N/A",
      ArchivedBy: record.archivedBy || "N/A",
      QualificationCount: record.qualifications?.length || 0
    }));

    const worksheet = utils.json_to_sheet(exportData);
    const workbook = utils.book_new();
    utils.book_append_sheet(workbook, worksheet, "Archived Records");
    writeFile(workbook, "Archived_Employee_Records.xlsx");
  };

  // Check admin access after all hooks
  if (userRole !== "admin") {
    return (
      <div style={{ padding: "20px", textAlign: "center" }}>
        <h2 style={{ color: "red" }}>❌ Access Denied</h2>
        <p>Only administrators can view archived data.</p>
      </div>
    );
  }

  if (loading) {
    return <p>Loading archived records...</p>;
  }

  const tableStyle = {
    width: "100%",
    borderCollapse: "collapse",
    fontSize: "14px",
    border: "1px solid #ddd",
    borderRadius: "10px",
    overflow: "hidden",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
  };

  const headerRowStyle = {
    backgroundColor: "#6c757d",
    color: "white",
    textAlign: "left",
  };

  const headerCellStyle = {
    padding: "12px",
    fontWeight: "bold",
    borderBottom: "2px solid #ddd",
  };

  const cellStyle = {
    padding: "10px",
    borderBottom: "1px solid #ddd",
    textAlign: "left",
  };

  const evenRowStyle = {
    backgroundColor: "#f9f9f9",
  };

  const oddRowStyle = {
    backgroundColor: "#ffffff",
  };

  return (
    <div style={{ padding: "20px", maxWidth: "100%", margin: "0 auto", fontFamily: "Arial, sans-serif" }}>
      <h2 style={{ textAlign: "center", marginBottom: "20px", color: "#6c757d" }}>
        🗂️ Archived Employee Records (Admin Only)
      </h2>

      {/* Search and Export */}
      <div style={{ marginBottom: "20px", display: "flex", gap: "10px", alignItems: "center", flexWrap: "wrap" }}>
        <input
          type="text"
          placeholder="Search archived records by Name or Employee ID..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{
            flex: 1,
            minWidth: "300px",
            padding: "10px",
            fontSize: "16px",
            border: "2px solid #6c757d",
            borderRadius: "25px",
            outline: "none",
            boxSizing: "border-box"
          }}
        />
        <button
          onClick={exportArchivedData}
          style={{
            padding: "10px 20px",
            backgroundColor: "#6c757d",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
            fontWeight: "bold"
          }}
        >
          📊 Export Archived Data
        </button>
      </div>

      {searchTerm.trim() && (
        <p style={{ fontSize: "14px", color: "#666", margin: "0 0 20px 0", fontStyle: "italic" }}>
          Showing {displayedResults.length} archived record{displayedResults.length !== 1 ? 's' : ''} for "{searchTerm}"
        </p>
      )}

      {displayedResults.length === 0 ? (
        <p style={{ textAlign: "center", fontSize: "18px", color: "#666" }}>
          {searchTerm.trim() ? `No archived records found matching "${searchTerm}"` : "No archived records found."}
        </p>
      ) : (
        <table style={tableStyle}>
          <thead>
            <tr style={headerRowStyle}>
              <th style={headerCellStyle}>Employee No</th>
              <th style={headerCellStyle}>Name</th>
              <th style={headerCellStyle}>Department</th>
              <th style={headerCellStyle}>Unit</th>
              <th style={headerCellStyle}>Rank</th>
              <th style={headerCellStyle}>Qualifications</th>
              <th style={headerCellStyle}>Archived At</th>
              <th style={headerCellStyle}>Archived By</th>
              <th style={headerCellStyle}>Actions</th>
            </tr>
          </thead>
          <tbody>
            {displayedResults.map((record, index) => (
              <tr key={record.id} style={index % 2 === 0 ? evenRowStyle : oddRowStyle}>
                <td style={cellStyle}>{record.employeeNo || "N/A"}</td>
                <td style={cellStyle}>{record.name || "N/A"}</td>
                <td style={cellStyle}>{record.departmentName || record.department || "N/A"}</td>
                <td style={cellStyle}>{record.unitName || record.unit || "N/A"}</td>
                <td style={cellStyle}>{record.rank || "N/A"}</td>
                <td style={cellStyle}>{record.qualifications?.length || 0} qualification(s)</td>
                <td style={cellStyle}>{record.archivedAt}</td>
                <td style={cellStyle}>{record.archivedBy || "N/A"}</td>
                <td style={cellStyle}>
                  <div style={{ display: "flex", gap: "5px", flexDirection: "column" }}>
                    <button
                      onClick={() => restoreEmployee(record.id, record)}
                      style={{
                        padding: "4px 8px",
                        backgroundColor: "#28a745",
                        color: "white",
                        border: "none",
                        borderRadius: "3px",
                        cursor: "pointer",
                        fontSize: "11px",
                        fontWeight: "bold"
                      }}
                      title="Restore to active employees"
                    >
                      🔄 Restore
                    </button>
                    <button
                      onClick={() => permanentlyDelete(record.id, record)}
                      style={{
                        padding: "4px 8px",
                        backgroundColor: "#dc3545",
                        color: "white",
                        border: "none",
                        borderRadius: "3px",
                        cursor: "pointer",
                        fontSize: "11px",
                        fontWeight: "bold"
                      }}
                      title="Permanently delete (cannot be undone)"
                    >
                      🗑️ Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      <div style={{ marginTop: "20px", padding: "15px", backgroundColor: "#f8f9fa", borderRadius: "5px", fontSize: "14px" }}>
        <h4 style={{ margin: "0 0 10px 0", color: "#6c757d" }}>📋 Archived Data Information:</h4>
        <ul style={{ margin: 0, paddingLeft: "20px", color: "#666" }}>
          <li><strong>Total Archived Records:</strong> {archivedRecords.length}</li>
          <li><strong>Restore:</strong> Move archived records back to active employees</li>
          <li><strong>Delete:</strong> Permanently remove records (cannot be undone)</li>
          <li><strong>Access:</strong> Only administrators can view and manage archived data</li>
        </ul>
      </div>
    </div>
  );
}

export default ArchivedData;
