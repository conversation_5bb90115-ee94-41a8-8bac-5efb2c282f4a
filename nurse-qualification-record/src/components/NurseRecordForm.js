import React, { useState, useEffect, useRef } from "react";
import { collection, getDocs } from 'firebase/firestore';
import { firestoreDB } from '../services/firebase';
import { doc, getDoc, setDoc, deleteDoc, updateDoc } from "firebase/firestore";
import { serverTimestamp } from "firebase/firestore"; // Import Firestore timestamp
import * as XLSX from "xlsx";
import { set } from "firebase/database";


// Remove a new qualification from the newQualifications array by index
// This is now a proper hook-based function inside the component.


function NurseRecordForm({ showImportInitially = false, userRole = "" }) {
  // Fetch qualifications from 'qualifications' collection, include doc ID
  const fetchQualifications = async () => {
    try {
      const querySnapshot = await getDocs(collection(firestoreDB, 'qualifications'));
      const fetchedQualifications = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));
      setQualifications(fetchedQualifications);
    } catch (error) {
      console.error('Error fetching qualifications:', error);
    }
  };
  // New qualifications to be added
  const [newQualifications, setNewQualifications] = useState([]);

  // Remove a new qualification from the newQualifications array by index
  const removeNewQualification = (index) => {
    const updated = [...newQualifications];
    updated.splice(index, 1);
    setNewQualifications(updated);
  };
  // Mobile detection for responsive design
  const isMobile = typeof window !== "undefined" && window.innerWidth <= 600;
  const [prccCourses, setPrccCourses] = useState([]); // New state for PRCC courses
  // Fellowship title state for HKANM Fellowship Program
  const [fellowshipTitles, setFellowshipTitles] = useState([]);
  // Fetch fellowship titles from Firebase on mount (or when needed)
  useEffect(() => {
    const fetchFellowshipTitles = async () => {
      try {
        const snapshot = await getDocs(collection(firestoreDB, "fellewshipTitles"));
        const list = snapshot.docs.map(doc => doc.data().name);
        setFellowshipTitles(list);
      } catch (error) {
        setFellowshipTitles([]);
      }
    };
    fetchFellowshipTitles();
  }, []);
  const [department, setDepartment] = useState("");
  const [unit, setUnit] = useState("");
  const [rank, setRank] = useState("");
  const [name, setName] = useState("");
  const [employeeNo, setEmployeeNo] = useState("");
  const [message, setMessage] = useState("");
  const [graduatedThisYear, setGraduatedThisYear] = useState(null);


  const [qualifications, setQualifications] = useState([]); // All qualifications from Firestore
  // const [qualificationOptions, setQualificationOptions] = useState([]); // Options for the "Type" dropdown
  const [selectedQualifications, setSelectedQualifications] = useState([]); // Existing qualifications from Firestore

  const [units, setUnits] = useState([]);
  const [departments, setDepartments] = useState([]);

  // const [selectedQualification, setSelectedQualification] = useState("");
  // const [options, setOptions] = useState([]);
  // const [selectedOption, setSelectedOption] = useState("");
  const years = Array.from(new Array(50), (_, index) => new Date().getFullYear() - index);
  // const [selectedSubtypes, setSelectedSubtypes] = useState({});
  const [selectedSubtypes, setSelectedSubtypes] = useState({});
  const [showImport, setShowImport] = useState(showImportInitially);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userPermissions, setUserPermissions] = useState({
    allowViewQualifications: true,
    allowDeleteQualifications: true
  });

  const messageRef = useRef(null);

  // Fetch qualifications and their options from Firestore
  useEffect(() => {
    fetchQualifications();

    const fetchDepartments = async () => {
      const departmentsRef = collection(firestoreDB, "departments");
      const querySnapshot = await getDocs(departmentsRef);

      const departmentData = querySnapshot.docs
        .filter((doc) => doc.id !== "archived_data") // Exclude archived_data from user selection
        .map((doc) => ({
          id: doc.id,
          name: doc.data().name,
        }));
      setDepartments(departmentData);
    };

    fetchDepartments();
  }, []);

  // Fetch PRCC courses
  useEffect(() => {
    const fetchPrccCourses = async () => {
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "prccCourses"));
        const fetchedPrccCourses = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));
        setPrccCourses(fetchedPrccCourses);
      } catch (error) {
        console.error("Error fetching PRCC courses:", error);
      }
    };

    fetchPrccCourses();
  }, []);

  // Fetch user permissions
  useEffect(() => {
    const fetchUserPermissions = async () => {
      try {
        const settingsRef = doc(firestoreDB, "system_settings", "user_permissions");
        const settingsSnap = await getDoc(settingsRef);

        if (settingsSnap.exists()) {
          const fetchedPermissions = settingsSnap.data();
          setUserPermissions(fetchedPermissions);
        } else {
          // Default permissions if settings don't exist
          setUserPermissions({
            allowViewQualifications: true,
            allowDeleteQualifications: true
          });
        }
      } catch (error) {
        console.error("Error fetching user permissions:", error);
        // Default to restricted permissions on error
        setUserPermissions({
          allowViewQualifications: false,
          allowDeleteQualifications: false
        });
      }
    };

    fetchUserPermissions();
  }, [userRole]);


  // Update options when a qualification is selected
  // useEffect(() => {
  //   if (selectedQualification) {
  //     const qualification = qualifications.find((q) => q.type === selectedQualification);
  //     setOptions(qualification ? qualification.options : []);
  //   } else {
  //     setOptions([]);
  //   }
  // }, [selectedQualification, qualifications]);


  // Fetch employee data by Employee Number
  const handleFetchByEmployeeNo = async () => {
    if (!employeeNo) {
      setMessage("Please enter a valid Employee Number.");
      return;
    }

    try {
      const docRef = doc(firestoreDB, "employees", employeeNo);
      const docSnap = await getDoc(docRef);
      const currentYear = new Date().getFullYear();

      if (docSnap.exists()) {
        const data = docSnap.data();

        // Always load basic employee information
        setDepartment(data.department || "");
        setUnit(data.unit || "");
        setRank(data.rank || "");
        setName(data.name || "");
        setGraduatedThisYear(
          data?.freshGrad === currentYear ? "yes" : "no"
        );

        // Only load qualifications if user has permission
        if (userPermissions.allowViewQualifications) {
          setSelectedQualifications(data.qualifications || []);
          setMessage("Employee data loaded successfully.");
        } else {
          setSelectedQualifications([]); // Don't load qualifications if no permission
          setMessage("Employee basic information loaded. Qualifications hidden by administrator.");
        }
      } else {
        setMessage("No data found for the given Employee Number.");
        resetForm(true);
      }
    } catch (error) {
      console.error("Error fetching employee data:", error);
      setMessage("An error occurred while fetching employee data.");
      resetForm();
    }
  };

  // Fetch units based on selected department
  useEffect(() => {
    const fetchUnits = async () => {
      if (department) {
        const unitsRef = collection(firestoreDB, `departments/${department}/units`);
        const querySnapshot = await getDocs(unitsRef);

        const unitData = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          name: doc.data().name,
        }));
        setUnits(unitData);
      } else {
        setUnits([]);
      }
    };

    fetchUnits();
  }, [department]);

  useEffect(() => {
    const userData = localStorage.getItem("userData");
    if (userData) {
      setIsLoggedIn(true);
    }
  }, []);

  // Add new qualification fields (for new qualifications only)
  const addQualification = () => {
    setNewQualifications([
      ...newQualifications,
      { type: "", subType: "", courseTitle: "", yearObtained: "", fellowshipTitle: "" },
    ]);
  };

  // Update a qualification in the NEW qualifications array
  const updateQualification = (index, field, value) => {
    const updated = [...newQualifications];
    updated[index][field] = value;

    if (field === "type") {
      // Dynamically update options for "Type" based on qualification type
      const selectedQualification = qualifications.find((q) => q.type === value);
      if (selectedQualification) {
        if (
          selectedQualification.type === "Resuscitation Training" ||
          selectedQualification.type === "CRM/Sim Training" ||
          selectedQualification.type === "Specialty Training"
        ) {
          const selectedOptions = selectedQualification.options.sort();
          updated[index].options = selectedOptions;
        } else {
          updated[index].options = selectedQualification.options || [];
        }
      }
      // Reset subType, courseTitle, and fellowshipTitle when type changes
      updated[index].subType = "";
      updated[index].courseTitle = "";
      updated[index].fellowshipTitle = "";
    }
    if (field === "subType") {
      // Reset courseTitle and fellowshipTitle when subType changes
      updated[index].courseTitle = "";
      updated[index].fellowshipTitle = "";
    }
    setNewQualifications(updated);
  };

  // Remove a qualification from EXISTING qualifications (in Firestore)
  const removeQualification = async (staffId, targetQual) => {
    try {
      const staffRef = doc(firestoreDB, 'employees', staffId);
      const staffSnap = await getDoc(staffRef);
  
      if (!staffSnap.exists()) {
        alert('Staff not found');
        return;
      }
  
      const data = staffSnap.data();
      const currentQualifications = data.qualifications || [];
  
      const updatedQualifications = currentQualifications.filter(qual => {
        return !(
          qual.type === targetQual.type &&
          qual.subType === targetQual.subType &&
          qual.yearObtained === targetQual.yearObtained &&
          (qual.courseTitle || '') === (targetQual.courseTitle || '') &&
          (qual.fellowshipTitle || '') === (targetQual.fellowshipTitle || '')
        );
      });
  
      await updateDoc(staffRef, { qualifications: updatedQualifications });
      setSelectedQualifications(updatedQualifications);
      alert('Qualification deleted');
    } catch (error) {
      console.error('Error:', error);
      alert('Failed to delete qualification');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Enhanced confirmation dialog with details
    const qualificationCount = newQualifications.length;
    const confirmationMessage = qualificationCount > 0
      ? `📝 Submit Qualification Records\n\n` +
        `Employee No: ${employeeNo || 'Not entered'}\n` +
        `Name: ${name || 'Not entered'}\n` +
        `Department: ${department || 'Not selected'}\n` +
        `Unit: ${unit || 'Not selected'}\n` +
        `Rank: ${rank || 'Not selected'}\n\n` +
        `New Qualifications to add: ${qualificationCount}\n\n` +
        `Are you sure you want to submit this information?`
      : `📝 Submit Employee Information\n\n` +
        `Employee No: ${employeeNo || 'Not entered'}\n` +
        `Name: ${name || 'Not entered'}\n` +
        `Department: ${department || 'Not selected'}\n` +
        `Unit: ${unit || 'Not selected'}\n` +
        `Rank: ${rank || 'Not selected'}\n\n` +
        `No new qualifications to add.\n\n` +
        `Are you sure you want to submit this information?`;

    const confirmed = window.confirm(confirmationMessage);
    if (!confirmed) {
      alert("❌ Submission cancelled by user.");
      return;
    }

    // Enhanced validation with detailed alerts
    if (!employeeNo) {
      const errorMsg = "❌ Employee Number Required!\n\nPlease enter an Employee Number before submitting.";
      alert(errorMsg);
      setMessage("Employee Number is required.");
      setTimeout(() => messageRef.current?.scrollIntoView({ behavior: "smooth", block: "start" }), 50);
      return;
    }
    if (isNaN(employeeNo)) {
      const errorMsg = "❌ Invalid Employee Number Format!\n\nEmployee Number must contain only numbers.\n\nExample: 12345";
      alert(errorMsg);
      setMessage("Employee Number must be numeric.");
      setTimeout(() => messageRef.current?.scrollIntoView({ behavior: "smooth", block: "start" }), 50);
      return;
    }
    if (Number(employeeNo) < 9999 || Number(employeeNo) > 400000) {
      const errorMsg = `❌ Employee Number Out of Range!\n\nEntered: ${employeeNo}\n\nValid range: 10000 - 400000\n\nPlease check and enter a valid Employee Number.`;
      alert(errorMsg);
      setMessage("Please input a valid Employee Number.");
      setTimeout(() => messageRef.current?.scrollIntoView({ behavior: "smooth", block: "start" }), 50);
      return;
    }

    // Enhanced qualification validation with detailed feedback
    const incompleteQualifications = newQualifications.filter(
      (qual, index) => {
        const missingFields = [];
        if (!qual.type) missingFields.push("Type");
        if (!qual.subType) missingFields.push("SubType");
        if (!qual.yearObtained) missingFields.push("Year Obtained");
        if (qual.type === "Specialty Training" && qual.subType === "HKANM Fellowship Program" && !qual.fellowshipTitle) {
          missingFields.push("Fellowship Title");
        }
        if (qual.type === "Specialty Training" && qual.subType === "PRCC - Post-registration Certificate Course " && !qual.courseTitle) {
          missingFields.push("Course Title");
        }

        if (missingFields.length > 0) {
          qual._missingFields = missingFields;
          qual._index = index + 1;
          return true;
        }
        return false;
      }
    );

    if (incompleteQualifications.length > 0) {
      let errorDetails = "❌ Incomplete Qualification Information!\n\n";
      incompleteQualifications.forEach(qual => {
        errorDetails += `Qualification ${qual._index}:\n`;
        errorDetails += `Missing: ${qual._missingFields.join(", ")}\n\n`;
      });
      errorDetails += "Please complete all required fields before submitting.";

      alert(errorDetails);
      setMessage("Please complete all items in the qualification.");
      setTimeout(() => messageRef.current?.scrollIntoView({ behavior: "smooth", block: "start" }), 50);
      return;
    }

    try {
      const docRef = doc(firestoreDB, "employees", employeeNo);
      const docSnap = await getDoc(docRef);
      let existingQualifications = docSnap.exists() ? docSnap.data().qualifications || [] : [];
      const isNewRecord = !docSnap.exists();

      // Filter out duplicate qualifications
      const filteredNewQualifications = newQualifications.filter((newQual) =>
        !existingQualifications.some(
          (existingQual) =>
            existingQual.type === newQual.type &&
            existingQual.subType === newQual.subType &&
            existingQual.courseTitle === newQual.courseTitle &&
            existingQual.yearObtained === newQual.yearObtained
        )
      );

      const updatedQualifications = [...existingQualifications, ...filteredNewQualifications];

      // Save to Firestore without overwriting existing qualifications
      await setDoc(
        docRef,
        {
          department: department || docSnap.data()?.department || "",
          unit: unit || docSnap.data()?.unit || "",
          rank: rank || docSnap.data()?.rank || "",
          name: name || docSnap.data()?.name || "",
          employeeNo,
          qualifications: updatedQualifications,
          freshGrad: graduatedThisYear === "yes" ? new Date().getFullYear() : null,
          lastUpdated: serverTimestamp(),
        },
        { merge: true }
      );

      await handleFetchByEmployeeNo(); // Re-fetch updated data

      // Simple success alert
      alert("✅ Records saved successfully!");

      setMessage(isNewRecord ? "New record created successfully!" : "Record updated successfully!");

      // Clear only the NEW qualifications input after successful submission
      setNewQualifications([]);

      // Scroll to top of page
      window.scrollTo({ top: 0, behavior: 'smooth' });

    } catch (error) {
      console.error("Error saving data:", error);
      alert("❌ Error saving data. Please try again.");
      setMessage("An error occurred while saving data.");
    }
  };

  // ✅ Function to reset the form
  const resetForm = (keepEmployeeNo = false) => {
    setDepartment("");
    setUnit("");
    setRank("");
    setName("");
    if (!keepEmployeeNo) setEmployeeNo("");
    setSelectedQualifications([]);
    setSelectedSubtypes({});
    setGraduatedThisYear("no");
    setNewQualifications([]);
  };

  // New function to handle Excel import
  const handleExcelImport = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: "array" });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      for (const row of jsonData) {
        const empNo = row.EmployeeNo.toString();
        if (!empNo || isNaN(empNo)) continue;

        const docRef = doc(firestoreDB, "employees", empNo);
        const docSnap = await getDoc(docRef);
        const existingData = docSnap.exists() ? docSnap.data() : {};

        const existingQualifications = existingData.qualifications || [];
        const newQual = row.QualificationType && row.QualificationSubType && row.CourseTitle && row.YearObtained
          ? {
            type: row.QualificationType,
            subType: row.QualificationSubType,
            courseTitle: row.CourseTitle,
            yearObtained: row.YearObtained,
          }
          : null;

        const isDuplicate = newQual
          ? existingQualifications.some(
            (q) =>
              q.type === newQual.type &&
              q.subType === newQual.subType &&
              q.courseTitle === newQual.courseTitle &&
              q.yearObtained === newQual.yearObtained
          )
          : true;

        const updatedQualifications = !isDuplicate && newQual
          ? [...existingQualifications, newQual]
          : existingQualifications;

        console.log("Importing EmployeeNo:", empNo);
        await setDoc(
          docRef,
          {
            department: row.Department || existingData.department || "",
            unit: row.Unit || existingData.unit || "",
            rank: row.Rank || existingData.rank || "",
            name: row.Name || existingData.name || "",
            employeeNo: empNo,
            appointmentDate: row.AppointmentDate || existingData.appointmentDate || "",
            rnAppointmentDate: row.RNAppointmentDate || existingData.rnAppointmentDate || "",
            qualifications: updatedQualifications,
            lastUpdated: serverTimestamp(),
          },
          { merge: true }
        );
      }

      setMessage("Excel import completed.");
    };
    reader.readAsArrayBuffer(file);
  };


  // Admin Excel import UI variables

  const excelInputStyle = {
    margin: "20px auto",
    padding: "10px",
    border: "1px solid #007BFF",
    borderRadius: "5px",
    backgroundColor: "#e9f5ff",
    textAlign: "center",
    maxWidth: "800px",
  };

  return (
    <>
      {isLoggedIn && (
        <div style={{ maxWidth: isMobile ? "95%" : "800px", margin: "20px auto", textAlign: "center" }}>
          <button
            type="button"
            onClick={() => setShowImport((prev) => !prev)}
            style={{
              padding: isMobile ? "8px 12px" : "10px 20px",
              backgroundColor: "#6c757d",
              color: "white",
              border: "none",
              borderRadius: "5px",
              marginBottom: "10px",
              fontSize: isMobile ? "14px" : undefined,
            }}
          >
            {showImport ? "Hide Import Data" : "Import Data"}
          </button>
        </div>
      )}
      {showImport && (
        <div style={{
          ...excelInputStyle,
          maxWidth: isMobile ? "95%" : "800px",
          padding: isMobile ? "10px" : "20px",
        }}>
          <h3>Admin: Import Staff Records via Excel</h3>
          <input type="file" accept=".xlsx, .xls" onChange={handleExcelImport} />
        </div>
      )}
      <form
        onSubmit={handleSubmit}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            e.preventDefault();
          }
        }}
        style={{
          maxWidth: isMobile ? "95%" : "800px",
          margin: "20px auto",
          padding: isMobile ? "10px" : "20px",
          border: "1px solid #ccc",
          borderRadius: "10px",
          background: "#f9f9f9",
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
          fontFamily: "Arial, sans-serif",
        }}
      >
        <h2 style={{ textAlign: "center", color: "#007BFF" }}>Nurse Qualification Records</h2>
        <div>
          <h3>Personal Information</h3>
          <label>
            Employee No *
            {userPermissions.allowViewQualifications
              ? ' (Please input Employee Number to "Get Data")'
              : ' (Get basic info only - qualifications hidden by admin)'}
          </label>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: isMobile ? "8px" : "10px",
              marginBottom: "10px",
              flexDirection: isMobile ? "column" : "row",
            }}
          >
            <input
              type="text"
              value={employeeNo}
              onChange={(e) => setEmployeeNo(e.target.value)}
              placeholder="Enter Employee Number"
              style={{
                flex: 1,
                padding: isMobile ? "6px" : "8px",
                border: "1px solid #ccc",
                borderRadius: "5px",
                width: isMobile ? "100%" : undefined,
                fontSize: isMobile ? "14px" : undefined,
              }}
            />
            <button
              type="button"
              onClick={handleFetchByEmployeeNo}
              style={{
                padding: isMobile ? "6px 12px" : "8px 16px",
                backgroundColor: userPermissions.allowViewQualifications ? "#007BFF" : "#ffc107",
                color: userPermissions.allowViewQualifications ? "white" : "#212529",
                border: "none",
                borderRadius: "5px",
                width: isMobile ? "100%" : undefined,
                fontSize: isMobile ? "14px" : undefined,
                fontWeight: "bold"
              }}
              title={userPermissions.allowViewQualifications
                ? "Get all employee data including qualifications"
                : "Get basic employee info only (qualifications hidden by admin)"}
            >
              {userPermissions.allowViewQualifications ? "Get Data" : "Get Basic Info"}
            </button>
          </div>
          {message && (
            <div
              ref={messageRef}
              style={{
                backgroundColor: "#ffff00",
                color: message.includes("successfully") ? "green" : "red",
                fontWeight: "bold",
                fontSize: "30px",
                textAlign: "center",
                padding: "10px",
                border: "1px solid #ffeeba",
                borderRadius: "5px",
                marginTop: "10px",
                marginBottom: "10px",
              }}
            >
              {message}
            </div>
          )}








          <label>Department</label>
          <select
            value={department}
            onChange={(e) => setDepartment(e.target.value)}
            style={{
              width: "100%",
              padding: isMobile ? "6px" : "8px",
              marginBottom: "10px",
              borderRadius: "5px",
              fontSize: isMobile ? "14px" : undefined,
            }}
          >
            <option value="">-- Select Department --</option>
            {departments.map((dept) => (
              <option key={dept.id} value={dept.id}>
                {dept.name}
              </option>
            ))}
          </select>

          <label>Unit</label>
          <select
            value={unit}
            onChange={(e) => setUnit(e.target.value)}
            style={{
              width: "100%",
              padding: isMobile ? "6px" : "8px",
              marginBottom: "10px",
              borderRadius: "5px",
              fontSize: isMobile ? "14px" : undefined,
            }}
          >
            <option value="">-- Select Unit --</option>
            {units.map((unit) => (
              <option key={unit.id} value={unit.name}>
                {unit.name}
              </option>
            ))}
          </select>

          <label>Rank</label>
          <select
            value={rank}
            onChange={(e) => setRank(e.target.value)}
            style={{
              width: "100%",
              padding: isMobile ? "6px" : "8px",
              marginBottom: "10px",
              borderRadius: "5px",
              fontSize: isMobile ? "14px" : undefined,
            }}
          >
            <option value="">-- Select Rank --</option>
            <option value="SNO">SNO</option>
            <option value="DOM">DOM</option>

            <option value="WM">WM</option>
            <option value="ANC">ANC</option>
            <option value="APN">APN</option>
            <option value="RN">RN</option>
            <option value="EN">EN</option>
            <option value="LOCUMRN">LocumRN</option>
            <option value="LOCUMEN">LocumEN</option>
            <option value="TUNS">TUNS</option>
          </select>

          <label>Name (Same as Staff Card)</label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value.toUpperCase())}
            placeholder="Enter Name"
            style={{
              width: "100%",
              padding: isMobile ? "6px" : "8px",
              marginBottom: "10px",
              borderRadius: "5px",
              boxSizing: "border-box",
              fontSize: isMobile ? "14px" : undefined,
            }}
          />

          {/* Add this after the Name input */}
          <div style={{ margin: "0 8px 10px 8px", padding: "8px 0" }}>
            <label>Are you graduated this year ({new Date().getFullYear()})? *</label>
            <div style={{
              display: "flex",
              gap: isMobile ? "10px" : "20px",
              marginTop: "8px",
              flexDirection: isMobile ? "column" : "row",
            }}>
              <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                <input
                  type="radio"
                  name="graduated"
                  value="yes"
                  checked={graduatedThisYear === "yes"}
                  onChange={(e) => setGraduatedThisYear(e.target.value)}
                  style={{ width: isMobile ? "16px" : undefined, height: isMobile ? "16px" : undefined }}
                />
                YES
              </label>
              <label style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                <input
                  type="radio"
                  name="graduated"
                  value="no"
                  checked={graduatedThisYear === "no"}
                  onChange={(e) => setGraduatedThisYear(e.target.value)}
                  style={{ width: isMobile ? "16px" : undefined, height: isMobile ? "16px" : undefined }}
                />
                NO
              </label>
            </div>
          </div>

          {/* <label>Year of Present Rank Appointment</label>
                <select
                  value={appointmentDate}
                  onChange={(e) => setAppointmentDate(e.target.value)}
                  style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}
                >
                  <option value="">-- Select Year --</option>
                  {years.map((year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select>

          <label>Year of RN Appointment in HA</label>
                <select
                  value={rnAppointmentDate}
                  onChange={(e) => setRnAppointmentDate(e.target.value)}
                  style={{ width: "100%", padding: "8px", marginBottom: "10px", borderRadius: "5px" }}
                >
                  <option value="">-- Select Year --</option>
                  {years.map((year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select> */}


          {selectedQualifications.length > 0 && userPermissions.allowViewQualifications && (
            <div style={{ margin: "20px 0" }}>
              <h4>Existing Qualifications</h4>
              <table style={{
                width: "100%",
                borderCollapse: "collapse",
                margin: "10px 0",
                border: "1px solid #ddd",
                borderRadius: "5px",
                overflow: "hidden",
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
                fontSize: isMobile ? "13px" : undefined,
              }}>
                <thead>
                  <tr style={{ backgroundColor: "#007BFF", color: "white" }}>
                    <th style={{ padding: isMobile ? "8px" : "12px", textAlign: "left" }}>Type</th>
                    <th style={{ padding: isMobile ? "8px" : "12px", textAlign: "left" }}>SubType</th>
                    <th style={{ padding: isMobile ? "8px" : "12px", textAlign: "left" }}>Course Title</th>
                    <th style={{ padding: isMobile ? "8px" : "12px", textAlign: "left" }}>Year Obtained</th>
                    <th style={{ padding: isMobile ? "8px" : "12px", textAlign: "left" }}>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {[...selectedQualifications]
                    .sort((a, b) => (b.yearObtained || 0) - (a.yearObtained || 0))
                    .map((qual, index) => (
                      <tr key={qual.id || index}
                        style={{
                          backgroundColor: index % 2 === 0 ? "#f9f9f9" : "white",
                          borderBottom: "1px solid #ddd"
                        }}
                      >
                        <td style={{ padding: isMobile ? "7px" : "10px" }}>
                          {qual.type}
                          {qual.id && (
                            <span style={{ color: "#aaa", fontSize: "10px", marginLeft: "4px" }}>
                              [{qual.id}]
                            </span>
                          )}
                        </td>
                        <td style={{ padding: isMobile ? "7px" : "10px" }}>{qual.subType}</td>
                        <td style={{ padding: isMobile ? "7px" : "10px" }}>
                          {qual.type === 'Specialty Training' && qual.subType === 'HKANM Fellowship Program'
                            ? qual.fellowshipTitle
                            : qual.courseTitle}
                        </td>
                        <td style={{ padding: isMobile ? "7px" : "10px" }}>{qual.yearObtained}</td>
                        <td style={{ padding: isMobile ? "7px" : "10px", textAlign: "center" }}>
                          {userPermissions.allowDeleteQualifications ? (
                            <button
                              type="button"
                              title="Remove"
                              onClick={() => {
                                const title = qual.courseTitle || qual.fellowshipTitle || '';
                                if (window.confirm(`Delete this qualification?\n\n${qual.type} - ${qual.subType} - ${title}`)) {
                                  removeQualification(employeeNo, qual);
                                }
                              }}
                              style={{
                                backgroundColor: "#dc3545",
                                color: "white",
                                border: "none",
                                borderRadius: "3px",
                                padding: "4px 8px",
                                cursor: "pointer"
                              }}
                            >
                              🗑️
                            </button>
                          ) : (
                            <span style={{
                              color: "#6c757d",
                              fontSize: "12px",
                              fontStyle: "italic"
                            }}>
                              🔒 Disabled
                            </span>
                          )}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Show message when qualifications are hidden due to permissions */}
          {employeeNo && name && !userPermissions.allowViewQualifications && (
            <div style={{
              margin: "20px 0",
              padding: "15px",
              backgroundColor: "#fff3cd",
              border: "1px solid #ffeaa7",
              borderRadius: "5px",
              color: "#856404"
            }}>
              <h4 style={{ margin: "0 0 10px 0", color: "#856404" }}>ℹ️ Existing Qualifications Hidden</h4>
              <p style={{ margin: 0, fontSize: "14px" }}>
                The view qualifications function has been disabled by the administrator.
                You can still add new qualifications below, but existing qualifications are not displayed.
              </p>
            </div>
          )}

          <h3>Qualifications</h3>
          {newQualifications.map((item, index) => {
            // Find the qualification object based on the selected type
            const qualification = qualifications.find((q) => q.type === item.type);
            // Helper for rendering
            const selectedSubtype = item.subType;
            return (
              <div key={index} style={{ marginBottom: "20px" }}>
                <div style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "10px",
                  flexDirection: isMobile ? "column" : "row",
                  gap: isMobile ? "8px" : undefined,
                }}>
                  <div style={{ flex: 1, width: isMobile ? "100%" : undefined }}>
                    <label>Qualification *</label>
                    <select
                      value={item.type}
                      onChange={(e) => updateQualification(index, "type", e.target.value)}
                      style={{
                        width: "100%",
                        padding: isMobile ? "6px" : "8px",
                        borderRadius: "5px",
                        fontSize: isMobile ? "14px" : undefined,
                      }}
                    >
                      <option value="">-- Select Qualification --</option>
                      {qualifications.map((q) => (
                        <option key={q.id} value={q.type}>
                          {q.title || q.type}
                        </option>
                      ))}
                    </select>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeNewQualification(index)}
                    style={{
                      backgroundColor: "#dc3545",
                      color: "white",
                      padding: isMobile ? "5px 10px" : "6px 12px",
                      border: "none",
                      borderRadius: "4px",
                      cursor: "pointer",
                      marginLeft: isMobile ? "0" : "10px",
                      float: isMobile ? undefined : "right",
                      fontSize: isMobile ? "14px" : undefined,
                      width: isMobile ? "100%" : undefined,
                    }}
                  >
                    Remove
                  </button>
                </div>
                {item.type && qualification?.options?.length > 0 && (
                  <>
                    <label>Type *</label>
                    <select
                      value={item.subType}
                      onChange={(e) => updateQualification(index, "subType", e.target.value)}
                      style={{
                        width: "100%",
                        padding: isMobile ? "6px" : "8px",
                        marginBottom: "10px",
                        borderRadius: "5px",
                        fontSize: isMobile ? "14px" : undefined,
                      }}
                    >
                      <option value="">-- Select Type --</option>
                      {qualification.options.map((option, idx) => (
                        <option key={idx} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </>
                )}
                {/* Fellowship Title dropdown for HKANM Fellowship Program */}
                {item.type === "Specialty Training" && selectedSubtype === "HKANM Fellowship Program" && (
                  <div>
                    <label>Fellowship Title</label>
                    <select
                      value={item.fellowshipTitle || ""}
                      onChange={(e) => updateQualification(index, "fellowshipTitle", e.target.value)}
                      style={{
                        width: "100%",
                        padding: isMobile ? "6px" : "8px",
                        marginBottom: "10px",
                        borderRadius: "5px",
                        fontSize: isMobile ? "14px" : undefined,
                      }}
                    >
                      <option value="">Select Title</option>
                      {fellowshipTitles.map((title, idx) => (
                        <option key={idx} value={title}>{title}</option>
                      ))}
                    </select>
                  </div>
                )}
                {/* Only show Course Title dropdown if subtype is PRCC - Post-registration Certificate Course */}
                {item.type === "Specialty Training" && selectedSubtype === "PRCC - Post-registration Certificate Course " && (
                  <div>
                    <label>Course Title</label>
                    <select
                      value={item.courseTitle}
                      onChange={(e) => updateQualification(index, "courseTitle", e.target.value)}
                      style={{
                        width: "100%",
                        padding: isMobile ? "6px" : "8px",
                        marginBottom: "10px",
                        borderRadius: "5px",
                        fontSize: isMobile ? "14px" : undefined,
                      }}
                    >
                      <option value="">-- Select PRCC Course --</option>
                      {prccCourses.map((course) => (
                        <option key={course.id} value={course.name}>
                          {course.name}
                        </option>
                      ))}
                    </select>
                  </div>
                )}
                {/* Show Course Title input if not PRCC, HKANM Fellowship, Resuscitation Training, or Ventilator Training */}
                {!(item.type === "Specialty Training" && (selectedSubtype === "PRCC - Post-registration Certificate Course " || selectedSubtype === "HKANM Fellowship Program")) && item.type !== "CRM/Sim Training" && item.type !== "Resuscitation Training" && item.type !== "Ventilator Training (Please refer to the list)" && (
                  <>
                    <label>Course Title</label>
                    <input
                      type="text"
                      value={item.courseTitle}
                      onChange={(e) => updateQualification(index, "courseTitle", e.target.value)}
                      placeholder="Enter Course Title if needed"
                      style={{
                        width: "100%",
                        padding: isMobile ? "6px" : "8px",
                        marginBottom: "10px",
                        borderRadius: "5px",
                        boxSizing: "border-box",
                        fontSize: isMobile ? "14px" : undefined,
                      }}
                    />
                  </>
                )}
                <label>
                  Year Obtained *
                  {item.type === "Resuscitation Training" ? " (Latest Training Year)" : ""}
                </label>
                <select
                  value={item.yearObtained}
                  onChange={(e) => updateQualification(index, "yearObtained", e.target.value)}
                  style={{
                    width: "100%",
                    padding: isMobile ? "6px" : "8px",
                    marginBottom: "10px",
                    borderRadius: "5px",
                    fontSize: isMobile ? "14px" : undefined,
                  }}
                >
                  <option value="">-- Select Year --</option>
                  {years.map((year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select>
              </div>
            );
          })}
          <div
            style={{
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              gap: isMobile ? "10px" : "15px",
              marginTop: "10px",
            }}
          >
            <button
              type="button"
              onClick={addQualification}
              style={{
                padding: isMobile ? "8px 12px" : "10px 20px",
                backgroundColor: "#007BFF",
                color: "white",
                border: "none",
                borderRadius: "5px",
                fontSize: isMobile ? "14px" : undefined,
                width: isMobile ? "100%" : undefined,
              }}
            >
              Add Qualification
            </button>
          </div>

        </div>

        <div
          style={{
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            gap: isMobile ? "10px" : "15px",
            marginTop: "20px",
          }}
        >
          <button
            type="submit"
            style={{
              width: "100%",
              padding: isMobile ? "8px" : "10px",
              backgroundColor: "#28a745",
              color: "white",
              border: "none",
              borderRadius: "5px",
              fontSize: isMobile ? "15px" : "16px",
            }}
          >
            Submit
          </button>
        </div>

      </form>
    </>
  );
}

export default NurseRecordForm;