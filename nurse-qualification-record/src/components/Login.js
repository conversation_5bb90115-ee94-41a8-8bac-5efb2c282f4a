import React, { useState } from "react";
import { auth, firestoreDB } from "../services/firebase"; 
import { signInWithEmailAndPassword, sendPasswordResetEmail } from "firebase/auth";
import { doc, getDoc } from "firebase/firestore";

function Login({ onLoginSuccess }) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showReset, setShowReset] = useState(false);
  const [resetEmail, setResetEmail] = useState("");
  const [resetSent, setResetSent] = useState(false);

  const handleLogin = async (e) => {
    e.preventDefault();
    setError(null);  // Clear previous errors
    setLoading(true);

    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      console.log("Login successful:", user);

      // Fetch user role from Firestore
      const userRef = doc(firestoreDB, "users", user.uid);
      const userSnap = await getDoc(userRef);

      if (!userSnap.exists()) {
        console.error("No user record found in Firestore.");
        setError("Access Denied. Please contact an administrator.");
      } else {
        console.log("User found in Firestore:", userSnap.data());
        if (onLoginSuccess) {
          onLoginSuccess(); // Call the success callback
        }
        alert("✅ Login Successful!"); // Popup for successful login
        setError(null); // Clear any existing errors
      }

    } catch (err) {
      console.error("Login error:", err.message);
      setError("❌ Failed to login. Please check your credentials.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ maxWidth: "400px", margin: "0 auto", padding: "20px", border: "1px solid #ddd", borderRadius: "5px", background: "#f9f9f9" }}>
      <h2 style={{ textAlign: "center" }}>Login</h2>
      {error && <p style={{ color: "red", textAlign: "center" }}>{error}</p>}
      <form onSubmit={handleLogin}>
        <div style={{ marginBottom: "15px" }}>
          <label>Email:</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            style={{ width: "100%", padding: "10px", border: "1px solid #ccc", borderRadius: "5px", boxSizing: "border-box"}}
          />
        </div>
        <div style={{ marginBottom: "15px" }}>
          <label>Password:</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            style={{ width: "100%", padding: "10px", border: "1px solid #ccc", borderRadius: "5px", boxSizing: "border-box" }}
          />
        </div>
        <button 
          type="submit" 
          disabled={loading} 
          style={{ width: "100%", padding: "10px", backgroundColor: "#007BFF", color: "white", border: "none", borderRadius: "5px" }}
        >
          {loading ? "Logging in..." : "Login"}
        </button>
        <button
          onClick={() => setShowReset(true)}
          style={{
            width: "100%",
            padding: "10px",
            marginTop: "10px",
            backgroundColor: "#FFA500",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer"
          }}
        >
          Forgot Password?
        </button>
      </form>

      {showReset && (
        <div style={{ 
          marginTop: "20px", 
          padding: "20px", 
          border: "1px solid #ddd", 
          borderRadius: "5px"
        }}>
          {resetSent ? (
            <div style={{ textAlign: "center" }}>
              <p>Password reset email sent to {resetEmail}</p>
              <button
                onClick={() => {
                  setShowReset(false);
                  setResetSent(false);
                }}
                style={{
                  marginTop: "10px",
                  padding: "8px 15px",
                  backgroundColor: "#007BFF",
                  color: "white",
                  border: "none",
                  borderRadius: "5px",
                  cursor: "pointer"
                }}
              >
                Back to Login
              </button>
            </div>
          ) : (
            <>
              <h3 style={{ textAlign: "center" }}>Reset Password</h3>
              <p style={{ marginBottom: "15px" }}>
                Enter your email to receive a password reset link
              </p>
              <div style={{ marginBottom: "15px" }}>
                <input
                  type="email"
                  placeholder="Your email"
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                  required
                  style={{ 
                    width: "100%", 
                    padding: "10px", 
                    border: "1px solid #ccc", 
                    borderRadius: "5px", 
                    boxSizing: "border-box"
                  }}
                />
              </div>
              <button
                onClick={async () => {
                  try {
                    await sendPasswordResetEmail(auth, resetEmail);
                    setResetSent(true);
                    setError(null);
                  } catch (err) {
                    setError(err.message);
                  }
                }}
                style={{
                  width: "100%",
                  padding: "10px",
                  backgroundColor: "#007BFF",
                  color: "white",
                  border: "none",
                  borderRadius: "5px",
                  cursor: "pointer"
                }}
              >
                Send Reset Link
              </button>
              <button
                onClick={() => setShowReset(false)}
                style={{
                  width: "100%",
                  padding: "10px",
                  marginTop: "10px",
                  backgroundColor: "#6c757d",
                  color: "white",
                  border: "none",
                  borderRadius: "5px",
                  cursor: "pointer"
                }}
              >
                Cancel
              </button>
            </>
          )}
        </div>
      )}
    </div>
  );
}

export default Login;
