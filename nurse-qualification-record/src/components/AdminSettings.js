import { useState, useEffect } from "react";
import { firestoreDB } from "../services/firebase";
import { doc, getDoc, setDoc } from "firebase/firestore";

function AdminSettings({ userRole }) {
  const [settings, setSettings] = useState({
    allowViewQualifications: true,
    allowDeleteQualifications: true
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Check admin access
  if (userRole !== "admin") {
    return (
      <div style={{ padding: "20px", textAlign: "center" }}>
        <h2 style={{ color: "red" }}>❌ Access Denied</h2>
        <p>Only administrators can access system settings.</p>
      </div>
    );
  }

  // Fetch current settings
  useEffect(() => {
    const fetchSettings = async () => {
      setLoading(true);
      try {
        const settingsRef = doc(firestoreDB, "system_settings", "user_permissions");
        const settingsSnap = await getDoc(settingsRef);
        
        if (settingsSnap.exists()) {
          setSettings(settingsSnap.data());
        } else {
          // Create default settings if they don't exist
          const defaultSettings = {
            allowViewQualifications: true,
            allowDeleteQualifications: true
          };
          await setDoc(settingsRef, defaultSettings);
          setSettings(defaultSettings);
        }
      } catch (error) {
        console.error("Error fetching settings:", error);
      }
      setLoading(false);
    };

    fetchSettings();
  }, []);

  // Save settings
  const saveSettings = async () => {
    setSaving(true);
    try {
      const settingsRef = doc(firestoreDB, "system_settings", "user_permissions");
      await setDoc(settingsRef, settings);
      alert("✅ Settings saved successfully!");
    } catch (error) {
      console.error("Error saving settings:", error);
      alert("❌ Failed to save settings. Please try again.");
    }
    setSaving(false);
  };

  // Toggle setting
  const toggleSetting = (settingKey) => {
    setSettings(prev => ({
      ...prev,
      [settingKey]: !prev[settingKey]
    }));
  };

  if (loading) {
    return <p>Loading settings...</p>;
  }

  const containerStyle = {
    maxWidth: "600px",
    margin: "0 auto",
    padding: "30px",
    border: "1px solid #ddd",
    borderRadius: "10px",
    backgroundColor: "#f9f9f9",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
    fontFamily: "Arial, sans-serif",
  };

  const headerStyle = {
    textAlign: "center",
    color: "#333",
    marginBottom: "30px",
    fontSize: "24px",
  };

  const settingItemStyle = {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "20px",
    marginBottom: "15px",
    backgroundColor: "white",
    border: "1px solid #ddd",
    borderRadius: "8px",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
  };

  const settingLabelStyle = {
    flex: 1,
    marginRight: "20px",
  };

  const settingTitleStyle = {
    fontSize: "16px",
    fontWeight: "bold",
    color: "#333",
    marginBottom: "5px",
  };

  const settingDescStyle = {
    fontSize: "14px",
    color: "#666",
    lineHeight: "1.4",
  };

  const toggleButtonStyle = (isEnabled) => ({
    padding: "8px 16px",
    border: "none",
    borderRadius: "20px",
    cursor: "pointer",
    fontWeight: "bold",
    fontSize: "14px",
    minWidth: "80px",
    backgroundColor: isEnabled ? "#28a745" : "#dc3545",
    color: "white",
    transition: "background-color 0.3s",
  });

  const saveButtonStyle = {
    width: "100%",
    padding: "12px",
    backgroundColor: "#007BFF",
    color: "white",
    border: "none",
    borderRadius: "5px",
    fontSize: "16px",
    fontWeight: "bold",
    cursor: "pointer",
    marginTop: "20px",
  };

  const disabledButtonStyle = {
    backgroundColor: "#ccc",
    cursor: "not-allowed",
  };

  return (
    <div style={containerStyle}>
      <h2 style={headerStyle}>⚙️ Admin Settings</h2>
      <p style={{ textAlign: "center", color: "#666", marginBottom: "30px" }}>
        Control user permissions for qualification management
      </p>

      <div style={settingItemStyle}>
        <div style={settingLabelStyle}>
          <div style={settingTitleStyle}>View Qualifications</div>
          <div style={settingDescStyle}>
            Allow users to retrieve and view existing qualification records using "Get Data" function.
            When disabled, users cannot view other employees' qualifications.
          </div>
        </div>
        <button
          onClick={() => toggleSetting('allowViewQualifications')}
          style={toggleButtonStyle(settings.allowViewQualifications)}
        >
          {settings.allowViewQualifications ? "ON" : "OFF"}
        </button>
      </div>

      <div style={settingItemStyle}>
        <div style={settingLabelStyle}>
          <div style={settingTitleStyle}>Delete Qualifications</div>
          <div style={settingDescStyle}>
            Allow users to delete qualification records from existing employee data.
            When disabled, users cannot delete any qualifications.
          </div>
        </div>
        <button
          onClick={() => toggleSetting('allowDeleteQualifications')}
          style={toggleButtonStyle(settings.allowDeleteQualifications)}
        >
          {settings.allowDeleteQualifications ? "ON" : "OFF"}
        </button>
      </div>

      <button
        onClick={saveSettings}
        disabled={saving}
        style={saving ? { ...saveButtonStyle, ...disabledButtonStyle } : saveButtonStyle}
      >
        {saving ? "Saving..." : "💾 Save Settings"}
      </button>

      <div style={{
        marginTop: "30px",
        padding: "20px",
        backgroundColor: "#e9ecef",
        borderRadius: "8px",
        fontSize: "14px",
        color: "#666"
      }}>
        <h4 style={{ margin: "0 0 10px 0", color: "#333" }}>📋 Settings Information:</h4>
        <ul style={{ margin: 0, paddingLeft: "20px" }}>
          <li><strong>View Qualifications:</strong> {settings.allowViewQualifications ? "Users can retrieve qualification data" : "Users cannot view existing qualifications"}</li>
          <li><strong>Delete Qualifications:</strong> {settings.allowDeleteQualifications ? "Users can delete qualifications" : "Users cannot delete any qualifications"}</li>
          <li><strong>Admin Access:</strong> These settings do not affect admin users</li>
          <li><strong>Real-time:</strong> Changes take effect immediately after saving</li>
        </ul>
      </div>
    </div>
  );
}

export default AdminSettings;
