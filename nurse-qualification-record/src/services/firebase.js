// src/services/firebase.js

// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getDatabase } from 'firebase/database';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyAZhF1TcErLt97u7a_Wllf_LUZzEtPSn1E",
  authDomain: "nursetrainingrecords.firebaseapp.com",
  projectId: "nursetrainingrecords",
  storageBucket: "nursetrainingrecords.firebasestorage.app",
  messagingSenderId: "100427310441",
  appId: "1:100427310441:web:4831f0a2d92e26937fc9f8",
  measurementId: "G-N929V9ND01",
  databaseURL: "https://nursetrainingrecords-default-rtdb.asia-southeast1.firebasedatabase.app/"
};

// Initialize Firebase


const app = initializeApp(firebaseConfig);
// const analytics = getAnalytics(app);
export const firestoreDB = getFirestore(app);
export const db = getDatabase(app);
export const auth = getAuth(app);