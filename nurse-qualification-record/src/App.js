import React, { useState, useEffect } from "react";
import Nurse<PERSON><PERSON>ordForm from "./components/NurseRecordForm";
import ImportData from "./components/ImportData";
import ViewResults from "./components/ViewResults";
import AddUsers from "./components/addUsers";
import Login from "./components/Login";
import UserList from "./components/UserList"; // User List Component
import ResetPassword from "./components/ResetPassword"; // Reset Password Component
import ArchivedData from "./components/ArchivedData"; // Archived Data Component
import AdminSettings from "./components/AdminSettings"; // Admin Settings Component

import { auth, firestoreDB } from "./services/firebase";
import { onAuthStateChanged, signOut } from "firebase/auth";
import { doc, getDoc } from "firebase/firestore";
// import addQualifications from "./scripts/addQualifications"; // Import script

function App() {
  const [currentView, setCurrentView] = useState("recordForm"); // Default view
  const [user, setUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [department, setDepartment] = useState(null);
  const [unit, setUnit] = useState(null);
  const [userDepartments, setUserDepartments] = useState(null);
  const [userUnits, setUserUnits] = useState(null);
  const [accessDenied, setAccessDenied] = useState(false);
  const [loading, setLoading] = useState(true);

  // Initialize qualifications on app load
//  useEffect(() => {
  //  const initializeQualifications = async () => {
 //     try {
 //       console.log("Initializing qualifications...");
 //       await addQualifications();
//        console.log("Qualifications initialized successfully.");
   //   } catch (error) {
  //      console.error("Error initializing qualifications:", error);
 //     }
 //   };
//   initializeQualifications();
//  }, []);

  // Fetch user and role information from Firestore
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setLoading(true); // Start loading
      if (user) {
        setUser(user);
        try {
          console.log("Fetching Firestore user data...");
          const userRef = doc(firestoreDB, "users", user.uid);
          const userSnap = await getDoc(userRef);

          if (userSnap.exists()) {
            const userData = userSnap.data();
            console.log("User Data:", userData);

            setUserRole(userData.role || "unit");
            setDepartment(userData.department || null);
            setUnit(userData.unit && userData.unit.trim() !== "" ? userData.unit : null);
            setUserDepartments(userData.departments || null);
            setUserUnits(userData.units || null);
            setAccessDenied(false);
          } else {
            console.log("User not found in Firestore. Access denied.");
            setUserRole(null);
            setAccessDenied(true);
          }
        } catch (error) {
          console.error("Error fetching user data:", error);
          setUserRole(null);
          setAccessDenied(true);
        }
      } else {
        console.log("No authenticated user.");
        setUser(null);
        setUserRole(null);
        setDepartment(null);
        setUnit(null);
        setUserDepartments(null);
        setUserUnits(null);
        setAccessDenied(false);
      }
      setLoading(false); // Stop loading
    });

    return () => unsubscribe();
  }, []);

  // Handle logout
  const handleLogout = () => {
    signOut(auth);
    setUser(null);
    setUserRole(null);
    setDepartment(null);
    setUnit(null);
    setUserDepartments(null);
    setUserUnits(null);
    setAccessDenied(false);
    setCurrentView("recordForm");
  };

  // Render content based on the current view
  const renderContent = () => {
    if (loading) {
      return <p style={{ color: "blue", textAlign: "center" }}>Loading...</p>;
    }
    
    if (accessDenied) {
      return <p style={{ color: "red", textAlign: "center" }}>Access Denied. Please contact an administrator.</p>;
    }

    switch (currentView) {
      
      case "recordForm":
        return <NurseRecordForm userRole={userRole} />;
      case "viewResults":
        return user ? (
          <ViewResults
            userRole={userRole}
            department={department}
            unit={unit}
            userDepartments={userDepartments}
            userUnits={userUnits}
          />
        ) : (
          <Login />
        );
      case "viewUsers":
        return userRole === "admin" ? <UserList /> : <p style={{ color: "red" }}>Access Denied</p>;
      case "addUsers":
        return userRole === "admin" ? <AddUsers /> : <p style={{ color: "red" }}>Access Denied</p>;
      case "importData":
        return userRole === "admin" ? <ImportData /> : <p style={{ color: "red" }}>Access Denied</p>;
      case "archivedData":
        return userRole === "admin" ? <ArchivedData userRole={userRole} /> : <p style={{ color: "red" }}>Access Denied</p>;
      case "adminSettings":
        return userRole === "admin" ? <AdminSettings userRole={userRole} /> : <p style={{ color: "red" }}>Access Denied</p>;
      case "login":
        return <Login onLoginSuccess={() => setCurrentView("viewResults")} />;
      case "resetPassword":
        return user ? <ResetPassword /> : <Login onLoginSuccess={() => setCurrentView("viewResults")} />;
      default:
        return <NurseRecordForm userRole={userRole} />;
    }
  };

  return (
    <div style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}>
      <h1>Nurses Qualification Records</h1>
      <div style={buttonContainerStyle}>
        <button onClick={() => setCurrentView("recordForm")} style={buttonStyle}>
          Add Record
        </button>
        <a
          href="https://docs.google.com/spreadsheets/d/1FFvnDAKJipygiboDnpYL3asup_g8vCjl/edit?usp=sharing&ouid=112745337246763701724&rtpof=true&sd=true"
          target="_blank"
          rel="noopener noreferrer"
          style={{ ...buttonStyle, backgroundColor: "#6A5ACD" }}
        >
          Qualification Input Reference List
        </a>
        {user ? (
          <>
            <button onClick={() => setCurrentView("viewResults")} style={buttonStyle}>
              View Results
            </button>
            {userRole === "admin" && (
              <button onClick={() => setCurrentView("viewUsers")} style={buttonStyle}>
                View Users
              </button>
            )}
            {userRole === "admin" && (
              <button onClick={() => setCurrentView("addUsers")} style={buttonStyle}>
                Manage Users
              </button>
            )}
            {userRole === "admin" && (
              <button onClick={() => setCurrentView("importData")} style={buttonStyle}>
                Import Data
              </button>
            )}
            {userRole === "admin" && (
              <button onClick={() => setCurrentView("archivedData")} style={archivedButtonStyle}>
                Archived Data
              </button>
            )}
            {userRole === "admin" && (
              <button onClick={() => setCurrentView("adminSettings")} style={settingsButtonStyle}>
                Settings
              </button>
            )}
            <button onClick={() => setCurrentView("resetPassword")} style={resetPasswordButtonStyle}>
              Reset Password
            </button>
            <button onClick={handleLogout} style={logoutButtonStyle}>
              Sign Out
            </button>
          </>
        ) : (
          <button
            onClick={() => setCurrentView("login")}
            style={{ ...buttonStyle, backgroundColor: "#007BFF" }}
          >
            Login (Admin Only)
          </button>
        )}
      </div>
      <hr style={{ margin: "20px 0" }} />
      {renderContent()}
    </div>
  );
}

// Responsive button styles
const buttonStyle = {
  margin: "5px",
  padding: "10px 20px",
  backgroundColor: "#4CAF50",
  color: "white",
  border: "none",
  borderRadius: "5px",
  cursor: "pointer",
  width: "auto",
};

const resetPasswordButtonStyle = {
  margin: "5px",
  padding: "10px 20px",
  backgroundColor: "#FFA500",
  color: "white",
  border: "none",
  borderRadius: "5px",
  cursor: "pointer",
  width: "auto",
};

const archivedButtonStyle = {
  margin: "5px",
  padding: "10px 20px",
  backgroundColor: "#6c757d",
  color: "white",
  border: "none",
  borderRadius: "5px",
  cursor: "pointer",
  width: "auto",
};

const settingsButtonStyle = {
  margin: "5px",
  padding: "10px 20px",
  backgroundColor: "#17a2b8",
  color: "white",
  border: "none",
  borderRadius: "5px",
  cursor: "pointer",
  width: "auto",
};

const logoutButtonStyle = {
  margin: "5px",
  padding: "10px 20px",
  backgroundColor: "#ff4d4d",
  color: "white",
  border: "none",
  borderRadius: "5px",
  cursor: "pointer",
  width: "auto",
};

const buttonContainerStyle = {
  display: "flex",
  flexWrap: "wrap",
  gap: "10px",
  justifyContent: "center",
  marginBottom: "20px",
};

export default App;
