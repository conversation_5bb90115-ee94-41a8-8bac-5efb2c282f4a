import { firestoreDB } from '../services/firebase';
import { collection, setDoc, doc } from 'firebase/firestore';

// PRCC Course Data
const prccCourses = [
  { id: 'critical_care_nursing', name: 'Critical Care Nursing' },
  { id: 'emergency_nursing', name: 'Emergency Nursing' },
  { id: 'perioperative_nursing', name: 'Perioperative Nursing' },
  { id: 'oncology_nursing', name: 'Oncology Nursing' },
  { id: 'paediatric_nursing', name: 'Paediatric Nursing' },
  { id: 'geriatric_nursing', name: 'Geriatric Nursing' },
  { id: 'orthopaedic_nursing', name: 'Orthopaedic Nursing' },
  { id: 'palliative_care_nursing', name: 'Palliative Care Nursing' },
  { id: 'psychiatric_nursing', name: 'Psychiatric Nursing' },
  { id: 'community_health_nursing', name: 'Community Health Nursing' },
];



const addPrccCourses = async () => {
  try {
    for (const course of prccCourses) {
      const courseRef = doc(collection(firestoreDB, 'prccCourses'), course.id);
      await setDoc(courseRef, {
        name: course.name
      });
    }
    console.log('PRCC Courses added successfully.');
  } catch (error) {
    console.error('Error adding PRCC courses:', error);
  }
};



// Run the functions
addPrccCourses();
