// src/scripts/importToFirestore.js

import { firestoreDB } from '../services/firebase'; // Adjust path to firebase.js
import { collection, doc, setDoc } from 'firebase/firestore';
import departmentData from './addDepartmentsData'; // Import your department data structure

const importToFirestore = async () => {
  try {
    for (const department of departmentData) {
      console.log(`Adding department: ${department.name}`);
      
      // Add Department to 'departments' collection
      const departmentRef = doc(firestoreDB, 'departments', department.id);
      await setDoc(departmentRef, { name: department.name });

      // Add Units as Subcollection under each Department
      for (const unit of department.units) {
        console.log(`  Adding unit: ${unit.name}`);
        const unitRef = doc(collection(departmentRef, 'units'), unit.id);
        await setDoc(unitRef, { name: unit.name });
      }
    }

    console.log('Data import completed successfully!');
  } catch (error) {
    console.error('Error importing data to Firestore:', error);
  }
};

// Run the script when this file is executed directly
importToFirestore();

export default importToFirestore;
