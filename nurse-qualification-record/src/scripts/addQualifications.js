import { collection, doc, setDoc } from "firebase/firestore";
import { firestoreDB } from "../services/firebase";

const addQualifications = async () => {
  try {
    const qualifications = [
      {
        id: "bachelor",
        type: "Bachelor",
        options: ["Nursing", "Clinical Specialty", "Management", "Others"], // Add the types here
      },
      {
        id: "master",
        type: "Master",
        options: ["Nursing", "Clinical Specialty", "Management", "Others"], // Add the types here
      },
      {
        id: "phd_other",
        type: "PHD/Other",
        options: ["Research", "Education", "Others"], // Example options for PHD/Other
      },
      {
        id: "specialty_training",
        type: "Specialty Training",
        options: ["First PRCC", "Second PRCC", "Third PRCC", "Fellow"], // Add relevant options
      },
      {
        id: "resuscitation_training",
        type: "Resuscitation Training",
        options: ["BLS", "ACLS", "AED Classroom", "AED E-learning", "MER"], // Add relevant options
      },
      {
        id: "ventilator_training",
        type: "Ventilator Training",
        options: ["Mechanical Ventilation Basics", "Advanced Ventilation Techniques"], // Example options
      },
      {
        id: "crm_sim_training",
        type: "CRM/Sim Training",
        options: ["CSEM", "DSS", "TTT Sim Training", "CRM Workshop"], // Add relevant options
      },
    ];

    for (const qualification of qualifications) {
      const docRef = doc(firestoreDB, "qualifications", qualification.id);
      await setDoc(docRef, {
        type: qualification.type,
        options: qualification.options,
      });
    }

    console.log("Qualifications with options added successfully.");
  } catch (error) {
    console.error("Error adding qualifications:", error);
  }
};

export default addQualifications;