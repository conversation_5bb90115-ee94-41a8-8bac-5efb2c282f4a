const { firestoreDB } = require('../services/firebase');
const { collection, setDoc, doc } = require('firebase/firestore');

const fellowshipsTitles = [
  { id: 'the_hong_kong_college_of_cardiac_nursing', name: 'The Hong Kong College of Cardiac Nursing' },
  { id: 'the_hong_kong_college_of_community_and_public_health_nursing', name: 'The Hong Kong College of Community and Public Health Nursing' },
  { id: 'the_hong_kong_college_of_critical_care_nursing', name: 'The Hong Kong College of Critical Care Nursing' },
  { id: 'the_hong_kong_college_of_education_and_research_in_nursing', name: 'The Hong Kong College of Education and Research in Nursing' },
  { id: 'the_hong_kong_college_of_emergency_nursing', name: 'The Hong Kong College of Emergency Nursing' },
  { id: 'the_hong_kong_college_of_gerontology_nursing', name: 'The Hong Kong College of Gerontology Nursing' },
  { id: 'the_hong_kong_college_of_medical_nursing', name: 'The Hong Kong College of Medical Nursing' },
  { id: 'the_hong_kong_college_of_mental_health_nursing', name: 'The Hong Kong College of Mental Health Nursing' },
  { id: 'the_hong_kong_college_of_midwives', name: 'The Hong Kong College of Midwives' },
  { id: 'the_hong_kong_college_of_nursing_and_health_care_management', name: 'The Hong Kong College of Nursing and Health Care Management' },
  { id: 'the_hong_kong_college_of_orthopaedic_nursing', name: 'The Hong Kong College of Orthopaedic Nursing' },
  { id: 'the_hong_kong_college_of_paediatric_nursing', name: 'The Hong Kong College of Paediatric Nursing' },
  { id: 'the_hong_kong_college_of_perioperative_nursing', name: 'The Hong Kong College of Perioperative Nursing' },
  { id: 'the_hong_kong_college_of_surgical_nursing', name: 'The Hong Kong College of Surgical Nursing' }
];

const addFellowshipsTitles = async () => {
  try {
    for (const title of fellowshipsTitles) {
      const docRef = doc(collection(firestoreDB, 'fellowships_title'), title.id);
      await setDoc(docRef, {
        id: title.id,
        name: title.name
      });
    }
    console.log('Fellowships titles added successfully.');
  } catch (error) {
    console.error('Error adding fellowships titles:', error);
  }
};

addFellowshipsTitles();