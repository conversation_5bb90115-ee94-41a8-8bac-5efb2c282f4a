// src/scripts/addDepartments.js

import { firestoreDB } from '../services/firebase'; // Adjust the path as needed
import { collection, doc, setDoc } from 'firebase/firestore';

// Data Structure: Departments and Units
const departmentData = [
  {
    id: 'medical',
    name: 'Medical',
    units: [
      { id: 'med_dom_office', name: 'Med DOM Office' },
      { id: 'ward_amc', name: 'Ward AMC' },
      { id: 'ward_g', name: 'Ward G' },
      { id: 'ward_2b', name: 'Ward 2B' },
      { id: 'ward_2s', name: 'Ward 2S' },
      { id: 'ward_3a', name: 'Ward 3A' },
      { id: 'ward_4a', name: 'Ward 4A' },
      { id: 'ward_4s', name: 'Ward 4S' },
      { id: 'ward_5a', name: 'Ward 5A' },
      { id: 'ward_5b', name: 'Ward 5B' },
      { id: 'ward_5s', name: 'Ward 5S' },
      { id: 'ward_8a', name: 'Ward 8A' },
      { id: 'ward_8b', name: 'Ward 8B' },
    ],
  },
  {
    id: 'surgery',
    name: 'Surgery',
    units: [
      { id: 'surg_dom_office', name: 'Surg DOM Office' },
      { id: 'ward_6a', name: 'Ward 6A' },
      { id: 'ward_6b', name: 'Ward 6B' },
      { id: 'ward_6s', name: 'Ward 6S' },
    ],
  },
  {
    id: 'accident_emergency',
    name: 'Accident & Emergency',
    units: [
      { id: 'a_e', name: 'A&E' },
      { id: 'ward_emw', name: 'Ward EMW' },
    ],
  },
  {
    id: 'anaesthetic_theatre',
    name: 'Anaesthetic and Operation Theatre',
    units: [
      { id: 'ass', name: 'ASS' },
      { id: 'a_ot', name: 'A&OT' },
      { id: 'idss', name: 'IDSS' },
    ],
  },
  {
    id: 'ambulatory_services',
    name: 'Ambulatory Services',
    units: [
      { id: 'sopd', name: 'SOPD' },
      { id: 'dmc', name: 'DMC' },
      { id: 'dm_centre', name: 'DM Centre' },
      { id: 'x_ray', name: 'X-ray' },
      { id: 'wound_stoma', name: 'Wound & Stoma' },
    ],
  },
  {
    id: 'orthopaedics_gynaecology',
    name: 'Orthopaedics & Traumatology & Gynaecology',
    units: [
      { id: 'o_t_dom_office', name: 'O&T DOM Office' },
      { id: 'ward_7a', name: 'Ward 7A' },
      { id: 'ward_7b', name: 'Ward 7B' },
      { id: 'ward_7s', name: 'Ward 7S' },
    ],
  },
  {
    id: 'medical_special_services',
    name: 'Medical Special Services (MSS)',
    units: [
      { id: 'hdc', name: 'HDC' },
      { id: 'ward_3b', name: 'Ward 3B' },
      { id: 'ward_4s', name: 'Ward 4S' },
      { id: 'mdrc', name: 'MDRC' },
    ],
  },
  {
    id: 'intensive_care',
    name: 'Intensive Care (ICU)',
    units: [
      { id: 'icu', name: 'ICU' },
      { id: 'ccu', name: 'CCU' },
    ],
  },
  {
    id: 'family_medical',
    name: 'Family Medical',
    units: [
      { id: 'family_medical', name: 'Family Medical' },
    ],
  },
  {
    id: 'ent_dental',
    name: 'ENT & Dental',
    units: [
      { id: 'ent', name: 'ENT' },
      { id: 'dent', name: 'DENT' },
      { id: 'eye', name: 'EYE' },
    ],
  },
  {
    id: 'general_outpatient_clinics',
    name: 'General Out-patient Clinics',
    units: [
      { id: 'gopc', name: 'GOPC' },
    ],
  },
];

// Function to Add Departments and Units to Firestore
const addDepartmentsAndUnits = async () => {
  try {
    for (const department of departmentData) {
      // Add Department to 'departments' collection
      const departmentRef = doc(firestoreDB, 'departments', department.id);
      await setDoc(departmentRef, { name: department.name });

      // Add Units to the subcollection 'units' under each department
      for (const unit of department.units) {
        const unitRef = doc(collection(departmentRef, 'units'), unit.id);
        await setDoc(unitRef, { name: unit.name });
      }
    }

    console.log('Departments and Units successfully added to Firestore!');
  } catch (error) {
    console.error('Error adding Departments and Units:', error);
  }
};



export default addDepartmentsAndUnits;