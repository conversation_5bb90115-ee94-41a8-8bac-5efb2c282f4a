const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

exports.setUserClaims = functions.auth.user().onCreate(async (user) => {
  if (user.email.endsWith('@tkoh.org')) {
    let role = 'viewer'; // Default role
    let unit = 'ward-2a';  // Default unit

    if (user.email === '<EMAIL>') {
      role = 'admin';
      unit = 'all';
    }

    try {
      await admin.auth().setCustomUserClaims(user.uid, { role, unit });
      console.log(`Custom claims set for ${user.email}: role=${role}, unit=${unit}`);
    } catch (error) {
      console.error('Error setting custom claims:', error);
    }
  }
});